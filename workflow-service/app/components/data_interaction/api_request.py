# backend/components/data_interaction/api_request.py

# --- Standard Library Imports ---
import asyncio
import json
import logging
import re
import tempfile
import time
from datetime import datetime, timezone
from pathlib import Path
from typing import List, Dict, Any, Optional, Tuple, ClassVar
from urllib.parse import urlencode, urlparse, urlunparse, parse_qsl

# --- Third-party Imports ---
try:
    import httpx

    HTTPX_AVAILABLE = True
except ImportError:
    HTTPX_AVAILABLE = False

try:
    import validators

    VALIDATORS_AVAILABLE = True
except ImportError:
    VALIDATORS_AVAILABLE = False

try:
    import aiofiles
    import aiofiles.os as aiofiles_os

    AIOFILES_AVAILABLE = True
except ImportError:
    AIOFILES_AVAILABLE = False

# --- Project Imports ---
from app.components.core.base_node import BaseNode
from app.models.workflow_builder.components import (
    InputBase,
    StringInput,
    ListInput,
    DropdownInput,
    DictInput,
    IntInput,
    BoolInput,
    MultilineInput,
    InputVisibilityRule,
    InputRequirementRule,
)
from app.models.workflow_builder.components import Output
from app.models.workflow_builder.context import WorkflowContext
from app.models.workflow_builder.node_result import NodeResult
from app.constants.semantic_types import API_RESPONSE, STATUS_INFO, METADATA, ERROR_INFO


# Configure logging
logger = logging.getLogger(__name__)


# --- Helper Functions (Keep as they are) ---
async def _save_response_to_file(
    response: httpx.Response, node_name: str
) -> Tuple[bool, Optional[Path]]:
    # ... (implementation remains the same) ...
    if not AIOFILES_AVAILABLE:
        logger.warning("aiofiles not installed. Cannot save response to file.")
        return False, None  # Indicate not saved

    content_type = response.headers.get("Content-Type", "").lower()
    binary_types = ["application/octet-stream", "application/pdf", "image/", "audio/", "video/"]
    is_binary = any(bt in content_type for bt in binary_types) or not response.encoding

    component_temp_dir = Path(tempfile.gettempdir()) / node_name
    try:
        await aiofiles_os.makedirs(component_temp_dir, exist_ok=True)
    except OSError as e:
        logger.error(f"Failed to create temporary directory {component_temp_dir}: {e}")
        return is_binary, None

    filename = None
    if "Content-Disposition" in response.headers:
        content_disposition = response.headers["Content-Disposition"]
        filename_match = re.search(
            r'filename\*?=(?:UTF-\d\'\')?\"?([^"]+)\"?', content_disposition, re.IGNORECASE
        )
        if filename_match:
            filename = filename_match.group(1).strip()
            filename = re.sub(r'[<>:"/\\|?*]', "_", filename)

    if not filename:
        try:
            url_path = urlparse(str(response.request.url)).path
            base_name = Path(url_path).name or "response"
            base_name = re.sub(r'[<>:"/\\|?*]', "_", base_name)
        except Exception:
            base_name = "response"

        extension_map = {
            "application/json": ".json",
            "text/html": ".html",
            "text/plain": ".txt",
            "text/csv": ".csv",
            "application/xml": ".xml",
            "image/jpeg": ".jpg",
            "image/png": ".png",
            "application/pdf": ".pdf",
        }
        extension = ".bin"
        for ct, ext in extension_map.items():
            if ct in content_type:
                extension = ext
                break
        filename = f"{base_name}{extension}"

    timestamp = datetime.now(timezone.utc).strftime("%Y%m%d%H%M%S%f")
    unique_filename = f"{timestamp}_{filename}"
    file_path = component_temp_dir / unique_filename

    try:
        if is_binary:
            async with aiofiles.open(file_path, "wb") as f:
                await f.write(response.content)
        else:
            encoding = response.encoding or "utf-8"
            async with aiofiles.open(file_path, "w", encoding=encoding, errors="replace") as f:
                await f.write(response.text)
        logger.info(f"Response saved to: {file_path}")
        return is_binary, file_path
    except Exception as e:
        logger.error(f"Error writing response to file {file_path}: {e}")
        try:
            await aiofiles_os.remove(file_path)
        except OSError:
            pass
        return is_binary, None


def _add_query_params(url: str, params: Optional[Dict[str, Any]]) -> str:
    # ... (implementation remains the same) ...
    if not params:
        return url
    try:
        url_parts = list(urlparse(url))
        query = dict(parse_qsl(url_parts[4]))
        str_params = {k: str(v) for k, v in params.items() if v is not None}
        query.update(str_params)
        url_parts[4] = urlencode(query)
        return urlunparse(url_parts)
    except Exception as e:
        logger.warning(f"Could not add query params to {url}: {e}")
        return url


def _process_dict_input(input_data: Any) -> Optional[Dict]:
    # ... (implementation remains the same) ...
    if input_data is None:
        return None
    if isinstance(input_data, dict):
        return input_data
    if isinstance(input_data, str):
        try:
            data = json.loads(input_data)
            if isinstance(data, dict):
                return data
            else:
                logger.warning("Decoded JSON from string is not a dictionary.")
                return None
        except json.JSONDecodeError:
            logger.warning("Input string is not valid JSON.")
            return None
    logger.warning(f"Input data is not a dictionary or valid JSON string: {type(input_data)}")
    return None


# ============================================================
# API Request Node Implementation (Single URL Version)
# ============================================================
class ApiRequestNode(BaseNode):
    """
    Makes a single HTTP request to a specified URL with configurable method, headers, body, etc.
    """

    # --- Node Definition ---
    name: ClassVar[str] = "ApiRequestNode"
    display_name: ClassVar[str] = "API Request"  # Indicate single URL capability
    description: ClassVar[str] = "Makes a single HTTP request to the specified URL."
    category: ClassVar[str] = "Data Interaction"
    icon: ClassVar[str] = "Globe"
    beta: ClassVar[bool] = False

    # Add debug print to see if this class is being loaded
    print("DEBUG: ApiRequestNode class loaded")

    inputs: ClassVar[List[InputBase]] = [
        StringInput(  # Changed from ListInput
            name="url",
            display_name="URL",
            info="The URL to make the request to.",
            value="",  # Default to empty string
            required=True,
            advanced=True,
            is_handle=True,
        ),
        DropdownInput(
            name="method",
            display_name="HTTP Method",
            options=["GET", "POST", "PUT", "PATCH", "DELETE"],
            value="GET",
            info="The HTTP method to use for the request.",
            real_time_refresh=True,
        ),
        DictInput(
            name="query_params",
            display_name="Query Parameters",
            info="Key-value pairs to append to the URL query string (optional).",
            value={},
            required=False,
            advanced=True,
            is_handle=True,  # Allow handle input for query params
        ),
        DictInput(
            name="headers",
            display_name="Request Headers",
            info="Key-value pairs for request headers (optional).",
            value={},
            required=False,
            advanced=True,
            is_handle=True,
        ),
        DictInput(
            name="body",
            display_name="Request Body (JSON)",
            info="Key-value dictionary for the request body (used for POST, PUT, PATCH). Will be JSON serialized.",
            value={},
            required=False,
            advanced=True,
            is_handle=True,
            visibility_rules=[
                InputVisibilityRule(field_name="method", field_value="POST"),
                InputVisibilityRule(field_name="method", field_value="PUT"),
                InputVisibilityRule(field_name="method", field_value="PATCH"),
            ],
            requirement_rules=[
                InputRequirementRule(field_name="method", field_value="POST"),
                InputRequirementRule(field_name="method", field_value="PUT"),
                InputRequirementRule(field_name="method", field_value="PATCH"),
            ],
            requirement_logic="OR",
            # Frontend Note: This field is shown and required when method is POST/PUT/PATCH
        ),
        # IntInput(
        #     name="timeout",
        #     display_name="Timeout (seconds)",
        #     value="100",
        #     info="Maximum time to wait for a response.",
        #     advanced=True,
        # ),
        # BoolInput(
        #     name="follow_redirects",
        #     display_name="Follow Redirects",
        #     value=True,
        #     info="Automatically follow HTTP redirects (e.g., 301, 302).",
        #     advanced=True,
        # ),
        # BoolInput(
        #     name="save_to_file",
        #     display_name="Save Response to File",
        #     value=False,
        #     info="Save response body to a temporary file instead of returning content directly. 'Result' output will be the file path.",
        #     advanced=True,
        # ),
        # DropdownInput(
        #     name="output_format",
        #     display_name="Output Format",
        #     options=["auto", "json", "text", "bytes", "file_path", "metadata_dict"],
        #     value="auto",
        #     info=(
        #         "'auto': Try JSON, fallback to text. "
        #         "'json': Force JSON parsing (error if invalid). "
        #         "'text': Return as text. "
        #         "'bytes': Return raw bytes. "
        #         "'file_path': Always save to file (requires 'Save Response to File'). "
        #         "'metadata_dict': Return dict with status, headers, body/path, error."
        #     ),
        #     advanced=True,
        # ),
        # BoolInput(
        #     name="raise_on_error",
        #     display_name="Raise Exception on HTTP Error",
        #     value=False,
        #     info="Stop workflow execution if an HTTP error status (4xx, 5xx) is received.",
        #     advanced=True,
        # ),
    ]

    # --- Modified Outputs for Single Result ---
    outputs: ClassVar[List[Output]] = [
        Output(
            name="data",
            display_name="Response Data/Body",
            output_type="Any",
            semantic_type=API_RESPONSE,
            description="Response content (JSON, text, bytes, file path, or dict based on Output Format).",
        ),
        Output(
            name="status_code",
            display_name="Status Code",
            output_type="int",  # Changed
            semantic_type=STATUS_INFO,
            description="HTTP status code received for the request.",
        ),
        Output(
            name="response_headers",
            display_name="Response Headers",
            output_type="dict",  # Changed
            semantic_type=METADATA,
            description="Response header dictionary received for the request.",
        ),
        Output(
            name="error",
            display_name="Error",
            output_type="str",  # Changed
            semantic_type=ERROR_INFO,
            description="Error message encountered during the request (empty string if successful).",
        ),
    ]

    async def execute(self, context: WorkflowContext) -> NodeResult:
        """
        Modern executor for the ApiRequestNode.
        Executes the single API request based on node configuration with proper dual-purpose input handling.
        """
        start_time = time.time()

        try:
            # Get input values using the proper dual-purpose input handling
            url = self.get_input_value("url", context, "")
            method = self.get_input_value("method", context, "GET").upper()
            query_params = self.get_input_value("query_params", context, {})
            headers = self.get_input_value("headers", context, {})
            body = self.get_input_value("body", context, {})
            timeout = self.get_input_value("timeout", context, None)
            follow_redirects = self.get_input_value("follow_redirects", context, True)
            save_to_file_flag = self.get_input_value("save_to_file", context, False)
            output_format = self.get_input_value("output_format", context, "auto")
            raise_on_error = self.get_input_value("raise_on_error", context, False)

            # Process inputs
            query_params = _process_dict_input(query_params)
            headers = _process_dict_input(headers)
            body = _process_dict_input(body)

            # Input validation
            if not isinstance(url, str) or not url:
                return NodeResult.error(
                    error_message="No URL provided.",
                    execution_time=time.time() - start_time
                )

            # Validate URL if validators is available
            is_valid = True
            if VALIDATORS_AVAILABLE:
                if not validators.url(url):
                    return NodeResult.error(
                        error_message=f"Invalid URL: {url}",
                        execution_time=time.time() - start_time
                    )

            # Check dependencies
            if not HTTPX_AVAILABLE:
                return NodeResult.error(
                    error_message="httpx library is required for ApiRequestNode. Please install it.",
                    execution_time=time.time() - start_time
                )

            # Check aiofiles availability if saving is requested
            if save_to_file_flag and not AIOFILES_AVAILABLE:
                context.log("aiofiles library not found, but 'Save Response to File' is enabled. File saving will be skipped.")
                save_to_file_flag = False

            # Prepare request body
            request_body_json = None
            if body and method in ["POST", "PUT", "PATCH"]:
                request_body_json = body

            # Execute the request
            async with httpx.AsyncClient(follow_redirects=follow_redirects, timeout=timeout, verify=False) as client:
                full_url = _add_query_params(url, query_params)
                try:
                    result_data, status, resp_headers, err_msg = await self._execute_single_request(
                        client=client,
                        method=method,
                        url=full_url,
                        headers=headers,
                        json_data=request_body_json,
                        save_flag=save_to_file_flag,
                        output_format_pref=output_format,
                        raise_on_http_error=raise_on_error,
                        original_url=url,
                    )
                except Exception as exc:
                    context.log(f"Request execution failed unexpectedly for {url}: {exc}")
                    result_data = None
                    status = 0
                    resp_headers = {}
                    err_msg = f"Request execution error: {exc}"

            # Return the result
            return NodeResult.success(
                outputs={
                    "result": result_data,
                    "status_code": status,
                    "response_headers": resp_headers,
                    "error": err_msg or "",
                },
                execution_time=time.time() - start_time
            )

        except Exception as e:
            error_msg = f"Error executing API request: {str(e)}"
            context.log(error_msg)
            return NodeResult.error(
                error_message=error_msg,
                execution_time=time.time() - start_time
            )

    def get_input_value(self, input_name: str, context: WorkflowContext, default: Any = None) -> Any:
        """
        Get the value of an input from the context.

        This method handles dual-purpose inputs, retrieving the value from the context.

        Args:
            input_name: The name of the input.
            context: The workflow execution context.
            default: The default value to return if the input is not found.

        Returns:
            The value of the input, or the default value if not found.
        """
        # Get the current node ID from the context
        node_id = context.current_node_id
        if not node_id:
            logger.warning("No current node ID in context")
            return default

        # Get all inputs for the current node
        node_inputs = context.node_outputs.get(node_id, {})

        # Check for the input value
        input_value = node_inputs.get(input_name)
        if input_value is not None:
            return input_value

        # If not found, return the default value
        return default

    # Legacy method for backward compatibility
    async def build(self, **kwargs) -> Dict[str, Any]:
        """
        Legacy executor for the ApiRequestNode.
        Executes the single API request based on node configuration.
        """
        print(f"Executing {self.name} using legacy build method...")
        if not HTTPX_AVAILABLE:
            raise ImportError("httpx library is required for ApiRequestNode. Please install it.")
        if not VALIDATORS_AVAILABLE:
            logger.warning("validators library not found. URL validation skipped.")

        # --- Get Parameters ---
        url: str = kwargs.get("url", "")  # Changed from urls
        method: str = kwargs.get("method", "GET").upper()
        query_params: Optional[Dict] = _process_dict_input(kwargs.get("query_params"))
        headers: Optional[Dict] = _process_dict_input(kwargs.get("headers"))
        body: Optional[Dict] = _process_dict_input(kwargs.get("body"))
        timeout: int = kwargs.get("timeout")
        follow_redirects: bool = kwargs.get("follow_redirects", True)
        save_to_file_flag: bool = kwargs.get("save_to_file", False)  # Get flag from kwargs
        output_format: str = kwargs.get("output_format", "auto")
        raise_on_error: bool = kwargs.get("raise_on_error", False)

        # --- Input Validation (Single URL) ---
        if not isinstance(url, str) or not url:
            logger.warning("ApiRequestNode: No URL provided.")
            # Return default/error state matching the single output structure
            return {
                "result": None,
                "status_code": 0,
                "response_headers": {},
                "error": "No URL provided.",
            }

        is_valid = False
        if VALIDATORS_AVAILABLE:
            if validators.url(url):
                is_valid = True
            else:
                logger.warning(f"Invalid URL provided: {url}")
        else:
            is_valid = True  # Skip validation

        if not is_valid:
            logger.error(f"ApiRequestNode: Invalid URL provided: {url}")
            return {
                "result": None,
                "status_code": 0,
                "response_headers": {},
                "error": f"Invalid URL: {url}",
            }

        # Check aiofiles availability if saving is requested
        if save_to_file_flag and not AIOFILES_AVAILABLE:
            logger.warning(
                "aiofiles library not found, but 'Save Response to File' is enabled. File saving will be skipped."
            )
            save_to_file_flag = False  # Disable saving if lib missing

        # --- Prepare Request Arguments ---
        request_body_json = None
        if body and method in ["POST", "PUT", "PATCH"]:
            request_body_json = body

        # --- Execute Single Request ---
        async with httpx.AsyncClient(follow_redirects=follow_redirects, timeout=timeout, verify=False) as client:
            full_url = _add_query_params(url, query_params)
            try:
                # Directly call the helper, no gather needed
                result_data, status, resp_headers, err_msg = await self._execute_single_request(
                    client=client,
                    method=method,
                    url=full_url,
                    headers=headers,
                    json_data=request_body_json,
                    save_flag=save_to_file_flag,
                    output_format_pref=output_format,
                    raise_on_http_error=raise_on_error,
                    original_url=url,  # Pass original URL for reporting
                )
            except Exception as exc:  # Catch potential exceptions *during* the call itself
                # This catches errors not handled within _execute_single_request's try/except
                logger.error(
                    f"Request execution failed unexpectedly for {url}: {exc}", exc_info=True
                )
                result_data = None
                status = 0  # Indicate failure
                resp_headers = {}
                err_msg = f"Request execution error: {exc}"

        # --- Return Output Dictionary (Single Result Structure) ---
        return {
            "result": result_data,
            "status_code": status,
            "response_headers": resp_headers,
            "error": err_msg or "",  # Ensure string type
        }

    async def _execute_single_request(
        self,
        client: httpx.AsyncClient,
        method: str,
        url: str,
        headers: Optional[Dict],
        json_data: Optional[Dict],
        save_flag: bool,
        output_format_pref: str,
        raise_on_http_error: bool,
        original_url: str,
    ) -> Tuple[Any, int, Dict, Optional[str]]:
        # ... (implementation of this helper function remains exactly the same) ...
        response: Optional[httpx.Response] = None
        error_message: Optional[str] = None
        status_code: int = 0
        response_headers: Dict = {}
        processed_result: Any = None
        file_path: Optional[Path] = None

        try:
            logger.info(f"Making {method} request to {url}")
            response = await client.request(
                method=method,
                url=url,
                headers=headers,
                json=json_data,
            )
            status_code = response.status_code
            response_headers = dict(response.headers)

            if raise_on_http_error:
                response.raise_for_status()

            should_save = save_flag or output_format_pref == "file_path"
            is_binary = False
            if should_save and AIOFILES_AVAILABLE:
                is_binary, file_path = await _save_response_to_file(response, self.name)
                if file_path:
                    processed_result = str(file_path)
                else:
                    logger.warning(f"File saving failed for {url}, returning content directly.")
                    should_save = False
            elif should_save and not AIOFILES_AVAILABLE:
                logger.warning(f"aiofiles not installed, cannot save response for {url}.")
                should_save = False

            if not should_save or not file_path:
                content_type = response.headers.get("Content-Type", "").lower()

                if output_format_pref == "bytes":
                    processed_result = response.content
                elif output_format_pref == "text":
                    processed_result = response.text
                elif output_format_pref == "json":
                    try:
                        processed_result = response.json()
                    except json.JSONDecodeError as e:
                        error_message = f"Failed to decode JSON: {e}"
                        logger.warning(f"{error_message} for {url}")
                        processed_result = None
                elif output_format_pref == "auto":
                    if "application/json" in content_type:
                        try:
                            processed_result = response.json()
                        except json.JSONDecodeError:
                            logger.warning(
                                f"Content-Type is JSON but decoding failed for {url}. Falling back to text."
                            )
                            processed_result = response.text
                    else:
                        processed_result = response.text
                elif output_format_pref == "file_path":
                    error_message = "Output format 'file_path' selected, but file saving failed."
                    logger.error(error_message)
                    processed_result = None
                elif output_format_pref == "metadata_dict":
                    pass
                else:
                    logger.warning(f"Invalid output_format '{output_format_pref}', using 'auto'.")
                    if "application/json" in content_type:
                        try:
                            processed_result = response.json()
                        except json.JSONDecodeError:
                            processed_result = response.text
                    else:
                        processed_result = response.text

            if output_format_pref == "metadata_dict":
                metadata = {
                    "status_code": status_code,
                    "headers": response_headers,
                    "url": url,
                    "original_url": original_url,
                    "error": None,
                }
                if file_path:
                    metadata["file_path"] = str(file_path)
                elif output_format_pref != "file_path":
                    metadata["body"] = processed_result
                processed_result = metadata

        except httpx.HTTPStatusError as e:
            error_message = f"HTTP Error {e.response.status_code} for url: {original_url}. Response: {e.response.text[:200]}"
            logger.error(error_message)
            status_code = e.response.status_code if e.response else 0
            response_headers = dict(e.response.headers) if e.response else {}
        except httpx.TimeoutException:
            error_message = f"Request timed out for {original_url}"
            logger.error(error_message)
            status_code = 408
        except httpx.RequestError as e:
            error_message = f"Request Error for {original_url}: {type(e).__name__}: {e}"
            logger.error(error_message)
            status_code = 503
        except Exception as e:
            error_message = (
                f"Unexpected error processing request for {original_url}: {type(e).__name__}: {e}"
            )
            logger.exception(error_message)
            status_code = 500

        if output_format_pref == "metadata_dict" and isinstance(processed_result, dict):
            processed_result["error"] = error_message

        return processed_result, status_code, response_headers, error_message
