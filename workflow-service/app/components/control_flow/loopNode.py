# workflow-service/app/components/control_flow/loopNode.py

from typing import List, Dict, Any, Optional, Literal
from pydantic import BaseModel, Field

from app.components.core.base_node import BaseNode
from app.models.workflow_builder.components import (
    InputBase,
    HandleInput,
    StringInput,
    BoolInput,
    IntInput,
    DropdownInput,
    InputVisibilityRule,
    Output,
)
from app.models.workflow_builder.context import WorkflowContext
from app.models.workflow_builder.node_result import NodeResult
from app.utils.workflow_builder.input_helpers import create_dual_purpose_input

# Pydantic Models for Loop Configuration
# (These remain unchanged, they are well-defined)

class RangeConfig(BaseModel):
    start: int = 1
    end: int = 10
    step: int = 1

class IterationSource(BaseModel):
    source_type: Literal["static_list", "number_range", "input_field"] = "input_field"
    static_items: Optional[List[Any]] = None
    range_config: Optional[RangeConfig] = None
    input_field_path: Optional[str] = "orders"


class ExitCondition(BaseModel):
    condition_type: Literal["all_items_processed", "max_iterations", "timeout", "success_condition", "failure_threshold"] = "all_items_processed"
    max_iterations: Optional[int] = None
    timeout_minutes: Optional[int] = None
    success_threshold: Optional[int] = None
    failure_threshold: Optional[int] = None


class IterationSettings(BaseModel):
    parallel_execution: bool = True
    max_concurrent: int = 3
    preserve_order: bool = True
    iteration_timeout: int = 60


class ResultAggregation(BaseModel):
    aggregation_type: Literal["collect_successful"] = "collect_successful"
    include_metadata: bool = True


class ErrorHandling(BaseModel):
    on_iteration_error: Literal["retry_once", "fail_fast", "continue"] = "retry_once"
    include_errors: bool = True


class LoopConfig(BaseModel):
    iteration_behavior: Literal["independent"] = "independent"
    iteration_source: IterationSource = Field(default_factory=IterationSource)
    exit_condition: ExitCondition = Field(default_factory=ExitCondition)
    iteration_settings: IterationSettings = Field(default_factory=IterationSettings)
    result_aggregation: ResultAggregation = Field(default_factory=ResultAggregation)
    error_handling: ErrorHandling = Field(default_factory=ErrorHandling)


class LoopNode(BaseNode):
    """
    A declarative node that iterates over a list of items based on a flexible configuration.

    This node defines the contract for a loop's behavior, but the actual execution
    and state management are handled by the orchestration engine. It specifies
    where to get the list, how to iterate, when to exit, and how to handle results and errors.
    """

    name: str = "LoopNode"
    display_name: str = "For Each Loop"
    description: str = "Iterates over a list, with advanced controls for parallelism, aggregation, and error handling."
    category: str = "Logic"
    icon: str = "Repeat"
    beta: bool = False

    inputs: List[InputBase] = [
        # === ITERATION SOURCE CONFIGURATION ===
        DropdownInput(
            name="iteration_source_type",
            display_name="Iteration Source Type",
            options=["input_field", "static_list", "number_range"],
            value="input_field",
            info="How to determine what items to iterate over.",
            advanced=False,
        ),

        # Input field source (when source_type is "input_field")
        create_dual_purpose_input(
            name="loop_input",
            display_name="Items to Process",
            input_type="string",
            required=False,
            info="The array of items to be processed by the loop. Can be connected from another node or entered directly.",
            input_types=["array", "list", "Any"],
            visibility_rules=[
                InputVisibilityRule(
                    field_name="iteration_source_type",
                    field_value="input_field",
                    operator="equals"
                )
            ],
        ),
        StringInput(
            name="input_field_path",
            display_name="Input Field Path",
            value="orders",
            info="Path to the field containing the array to iterate over (e.g., 'items', 'data.records', 'result.list').",
            visibility_rules=[
                InputVisibilityRule(
                    field_name="iteration_source_type",
                    field_value="input_field",
                    operator="equals"
                )
            ],
        ),

        # Static list source (when source_type is "static_list")
        create_dual_purpose_input(
            name="static_items",
            display_name="Static Items List",
            input_type="string",
            required=False,
            value='["item1", "item2", "item3"]',
            info="JSON array of items to iterate over. Example: [\"item1\", \"item2\"] or [1, 2, 3, 4]",
            input_types=["array", "list", "string", "Any"],
            visibility_rules=[
                InputVisibilityRule(
                    field_name="iteration_source_type",
                    field_value="static_list",
                    operator="equals"
                )
            ],
        ),

        # Number range source (when source_type is "number_range")
        IntInput(
            name="range_start",
            display_name="Range Start",
            value=1,
            min_value=0,
            info="Starting number for the range.",
            visibility_rules=[
                InputVisibilityRule(
                    field_name="iteration_source_type",
                    field_value="number_range",
                    operator="equals"
                )
            ],
        ),
        IntInput(
            name="range_end",
            display_name="Range End",
            value=10,
            min_value=1,
            info="Ending number for the range (inclusive).",
            visibility_rules=[
                InputVisibilityRule(
                    field_name="iteration_source_type",
                    field_value="number_range",
                    operator="equals"
                )
            ],
        ),
        IntInput(
            name="range_step",
            display_name="Range Step",
            value=1,
            min_value=1,
            max_value=100,
            info="Step size for the range.",
            visibility_rules=[
                InputVisibilityRule(
                    field_name="iteration_source_type",
                    field_value="number_range",
                    operator="equals"
                )
            ],
        ),

        # === EXIT CONDITION CONFIGURATION ===
        DropdownInput(
            name="exit_condition_type",
            display_name="Exit Condition",
            options=["all_items_processed", "max_iterations", "timeout", "success_condition", "failure_threshold"],
            value="all_items_processed",
            info="When to exit the loop.",
            advanced=False,
        ),
        IntInput(
            name="max_iterations",
            display_name="Max Iterations",
            value=100,
            min_value=1,
            info="Maximum number of iterations before exiting.",
            visibility_rules=[
                InputVisibilityRule(
                    field_name="exit_condition_type",
                    field_value="max_iterations",
                    operator="equals"
                )
            ],
        ),
        IntInput(
            name="timeout_minutes",
            display_name="Timeout (minutes)",
            value=30,
            min_value=1,
            max_value=60,
            info="Maximum time in minutes before exiting.",
            visibility_rules=[
                InputVisibilityRule(
                    field_name="exit_condition_type",
                    field_value="timeout",
                    operator="equals"
                )
            ],
        ),
        IntInput(
            name="success_threshold",
            display_name="Success Threshold",
            value=5,
            min_value=1,
            info="Number of successful iterations needed before exiting.",
            visibility_rules=[
                InputVisibilityRule(
                    field_name="exit_condition_type",
                    field_value="success_condition",
                    operator="equals"
                )
            ],
        ),
        IntInput(
            name="failure_threshold",
            display_name="Failure Threshold",
            value=3,
            min_value=1,
            max_value=10,
            info="Number of consecutive failures before exiting.",
            visibility_rules=[
                InputVisibilityRule(
                    field_name="exit_condition_type",
                    field_value="failure_threshold",
                    operator="equals"
                )
            ],
        ),

        # === ITERATION SETTINGS ===
        BoolInput(
            name="parallel_execution",
            display_name="Parallel Execution",
            value=True,
            info="Execute loop iterations in parallel for better performance.",
            advanced=False,  # Core setting, not advanced
        ),
        IntInput(
            name="max_concurrent",
            display_name="Max Concurrent Iterations",
            value=3,
            min_value=1,
            max_value=20,
            info="Maximum number of iterations to run concurrently (1-20).",
            visibility_rules=[
                InputVisibilityRule(
                    field_name="parallel_execution",
                    field_value=True,
                    operator="equals"
                )
            ],
        ),
        BoolInput(
            name="preserve_order",
            display_name="Preserve Order",
            value=True,
            info="Maintain the original order of items in the results.",
            advanced=False,
        ),
        IntInput(
            name="iteration_timeout",
            display_name="Iteration Timeout (seconds)",
            value=60,
            min_value=1,
            max_value=3600,
            info="Maximum time to wait for each iteration to complete (1-3600 seconds).",
            advanced=True,
        ),

        # === RESULT AGGREGATION ===
        DropdownInput(
            name="aggregation_type",
            display_name="Result Aggregation",
            options=["collect_all", "collect_successful", "count_only", "latest_only", "first_success", "combine_text"],
            value="collect_all",
            info="How to aggregate results from all iterations.",
            advanced=False,
        ),
        BoolInput(
            name="include_metadata",
            display_name="Include Metadata",
            value=True,
            info="Include metadata (timing, iteration index, etc.) in results.",
            advanced=True,
        ),

        # === ERROR HANDLING ===
        DropdownInput(
            name="on_iteration_error",
            display_name="On Iteration Error",
            options=["continue", "retry_once", "retry_twice", "exit_loop"],
            value="continue",
            info="How to handle errors in individual iterations.",
            advanced=False,
        ),
        BoolInput(
            name="include_errors",
            display_name="Include Errors in Results",
            value=True,
            info="Include error information in the final results.",
            advanced=True,
        ),
    ]

    outputs: List[Output] = [
        Output(
            name="current_item",
            display_name="Current Order (Iteration Output)",
            output_type="object",
            info="Outputs the item for the current iteration of the loop.",
            result_path_hint="current_iteration_item",
        ),
        Output(
            name="final_results",
            display_name="All Results (Exit Output)",
            output_type="array",
            info="Outputs the aggregated results after all iterations are complete.",
            result_path_hint="aggregated_results",
        ),
    ]

    # CORRECT: Define a default instance of LoopConfig on the class.
    # This serves as the template for get_definition and for new instances.
    loop_config: LoopConfig = LoopConfig()

    @classmethod
    def get_definition(cls) -> Dict[str, Any]:
        """
        Generates the JSON-serializable definition for the frontend.
        """
        definition = super().get_definition()
        # This now works because cls.loop_config is a Pydantic model instance.
        definition["loop_config"] = cls.loop_config.model_dump()
        return definition

    def __init__(self, **data: Any):
        """
        Initializes the LoopNode instance.

        Builds the loop_config from input values, ensuring every instance gets its own copy
        and preventing shared state issues between different loop nodes in a workflow.
        """
        super().__init__(**data)

        # Build loop_config from input values in the config
        config = data.get("config", {})

        # Extract iteration settings from inputs
        iteration_settings = IterationSettings(
            parallel_execution=config.get("parallel_execution", True),
            max_concurrent=config.get("max_concurrent", 3),
            preserve_order=config.get("preserve_order", True),
            iteration_timeout=config.get("iteration_timeout", 60)
        )

        # Extract result aggregation from inputs
        result_aggregation = ResultAggregation(
            aggregation_type=config.get("aggregation_type", "collect_successful"),
            include_metadata=config.get("include_metadata", True)
        )

        # Extract error handling from inputs
        error_handling = ErrorHandling(
            on_iteration_error=config.get("on_iteration_error", "retry_once"),
            include_errors=config.get("include_errors", True)
        )

        # Extract iteration source from inputs
        source_type = config.get("iteration_source_type", "input_field")
        iteration_source_data = {"source_type": source_type}

        if source_type == "input_field":
            iteration_source_data["input_field_path"] = config.get("input_field_path", "orders")
        elif source_type == "static_list":
            # Parse static items from string if needed
            static_items = config.get("static_items", [])
            if isinstance(static_items, str):
                try:
                    import json
                    static_items = json.loads(static_items)
                except (json.JSONDecodeError, ValueError):
                    static_items = ["item1", "item2", "item3"]  # Default fallback
            iteration_source_data["static_items"] = static_items
        elif source_type == "number_range":
            iteration_source_data["range_config"] = RangeConfig(
                start=config.get("range_start", 1),
                end=config.get("range_end", 10),
                step=config.get("range_step", 1)
            )

        iteration_source = IterationSource(**iteration_source_data)

        # Extract exit condition from inputs
        exit_condition_type = config.get("exit_condition_type", "all_items_processed")
        exit_condition_data = {"condition_type": exit_condition_type}

        if exit_condition_type == "max_iterations":
            exit_condition_data["max_iterations"] = config.get("max_iterations", 100)
        elif exit_condition_type == "timeout":
            exit_condition_data["timeout_minutes"] = config.get("timeout_minutes", 30)
        elif exit_condition_type == "success_condition":
            exit_condition_data["success_threshold"] = config.get("success_threshold", 5)
        elif exit_condition_type == "failure_threshold":
            exit_condition_data["failure_threshold"] = config.get("failure_threshold", 3)

        exit_condition = ExitCondition(**exit_condition_data)

        # Build complete loop_config
        self.loop_config = LoopConfig(
            iteration_behavior="independent",
            iteration_source=iteration_source,
            exit_condition=exit_condition,
            iteration_settings=iteration_settings,
            result_aggregation=result_aggregation,
            error_handling=error_handling
        )

    async def execute(self, context: WorkflowContext) -> NodeResult:
        """
        Execute the loop node.

        Note: This is a declarative node - the actual loop execution is handled
        by the orchestration engine. This method validates inputs and returns
        the loop configuration.
        """
        try:
            # Extract the loop input
            loop_input = self.get_input_value("loop_input", context)

            if not loop_input:
                return NodeResult(
                    success=False,
                    error="No input data provided for loop processing"
                )

            # Validate that we have an iterable
            if not isinstance(loop_input, (list, tuple)):
                # Try to convert string representation to list if needed
                if isinstance(loop_input, str):
                    try:
                        import json
                        loop_input = json.loads(loop_input)
                        if not isinstance(loop_input, (list, tuple)):
                            raise ValueError("Input must be an array")
                    except (json.JSONDecodeError, ValueError):
                        return NodeResult(
                            success=False,
                            error="Loop input must be an array or valid JSON array string"
                        )
                else:
                    return NodeResult(
                        success=False,
                        error="Loop input must be an array"
                    )

            # Return success with loop configuration
            # The orchestration engine will use this configuration to execute the loop
            return NodeResult(
                success=True,
                data={
                    "loop_config": self.loop_config.model_dump(),
                    "input_data": loop_input,
                    "total_items": len(loop_input)
                }
            )

        except Exception as e:
            return NodeResult(
                success=False,
                error=f"Loop node execution failed: {str(e)}"
            )

    def execute(self, **kwargs) -> Dict[str, Any]:
        """
        The execute method for LoopNode is a placeholder.

        The orchestration engine is responsible for interpreting the `loop_config`
        and managing the iteration, state, and execution flow. This method
        is not intended to be called directly for loop logic.
        """
        print(
            f"INFO ({self.name}): Execute called. Loop logic is handled by the orchestration engine, not this node."
        )
        return {}