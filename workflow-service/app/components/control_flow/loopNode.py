# workflow-service/app/components/control_flow/loopNode.py

from typing import List, Dict, Any, Optional, Literal
from pydantic import BaseModel, Field

from app.components.core.base_node import BaseNode
from app.models.workflow_builder.components import (
    InputBase,
    HandleInput,
    StringInput,
    BoolInput,
    IntInput,
    DropdownInput,
    InputVisibilityRule,
    InputRequirementRule,
    Output,
)
from app.models.workflow_builder.context import WorkflowContext
from app.models.workflow_builder.node_result import NodeResult
from app.utils.workflow_builder.input_helpers import create_dual_purpose_input

# Pydantic Models for Loop Configuration
# (These remain unchanged, they are well-defined)

class RangeConfig(BaseModel):
    start: int = 1
    end: int = 10
    step: int = 1

class IterationSource(BaseModel):
    source_type: Literal["list", "number_range"] = "list"
    static_items: Optional[List[Any]] = None
    list_step_size: Optional[int] = 1  # For batching list items
    range_config: Optional[RangeConfig] = None


class ExitCondition(BaseModel):
    condition_type: Literal["all_items_processed"] = "all_items_processed"


class IterationSettings(BaseModel):
    parallel_execution: bool = True
    max_concurrent: int = 3
    preserve_order: bool = True
    iteration_timeout: int = 60


class ResultAggregation(BaseModel):
    aggregation_type: Literal["collect_successful"] = "collect_successful"
    include_metadata: bool = True


class ErrorHandling(BaseModel):
    on_iteration_error: Literal["retry_once", "fail_fast", "continue"] = "retry_once"
    include_errors: bool = True


class LoopConfig(BaseModel):
    iteration_behavior: Literal["independent"] = "independent"
    iteration_source: IterationSource = Field(default_factory=IterationSource)
    exit_condition: ExitCondition = Field(default_factory=ExitCondition)
    iteration_settings: IterationSettings = Field(default_factory=IterationSettings)
    result_aggregation: ResultAggregation = Field(default_factory=ResultAggregation)
    error_handling: ErrorHandling = Field(default_factory=ErrorHandling)


class LoopNode(BaseNode):
    """
    A declarative node that iterates over a list of items based on a flexible configuration.

    This node defines the contract for a loop's behavior, but the actual execution
    and state management are handled by the orchestration engine. It specifies
    where to get the list, how to iterate, when to exit, and how to handle results and errors.
    """

    name: str = "LoopNode"
    display_name: str = "For Each Loop"
    description: str = "Iterates over a list, with advanced controls for parallelism, aggregation, and error handling."
    category: str = "Logic"
    icon: str = "Repeat"
    beta: bool = False

    inputs: List[InputBase] = [
        # === ITERATION SOURCE CONFIGURATION ===
        DropdownInput(
            name="iteration_source_type",
            display_name="Iteration Source",
            options=["list", "number_range"],
            value="list",
            info="Choose whether to iterate over a list of items or a number range.",
            real_time_refresh=True,
        ),

        # List source (when source_type is "list")
        create_dual_purpose_input(
            name="items_list",
            display_name="Items to Process",
            input_type="string",
            required=False,
            info="The array of items to be processed by the loop. Can be connected from another node or entered directly as JSON array.",
            input_types=["array", "list", "Any"],
            visibility_rules=[
                InputVisibilityRule(
                    field_name="iteration_source_type",
                    field_value="list",
                    operator="equals"
                )
            ],
            requirement_rules=[
                InputRequirementRule(
                    field_name="iteration_source_type",
                    field_value="list",
                    operator="equals"
                )
            ],
        ),
        create_dual_purpose_input(
            name="list_step_size",
            display_name="Batch Size",
            input_type="number",
            required=False,
            value=1,
            info="Number of items to process together in each iteration. For example, step size 2 will process items in pairs: [item1, item2], [item3, item4], etc.",
            input_types=["number", "integer", "Any"],
            visibility_rules=[
                InputVisibilityRule(
                    field_name="iteration_source_type",
                    field_value="list",
                    operator="equals"
                )
            ],
        ),

        # Number range source (when source_type is "number_range")
        create_dual_purpose_input(
            name="range_start",
            display_name="Start Value",
            input_type="number",
            required=False,
            value=1,
            info="Starting number for the range. Can be connected from another node or entered directly.",
            input_types=["number", "integer", "Any"],
            visibility_rules=[
                InputVisibilityRule(
                    field_name="iteration_source_type",
                    field_value="number_range",
                    operator="equals"
                )
            ],
            requirement_rules=[
                InputRequirementRule(
                    field_name="iteration_source_type",
                    field_value="number_range",
                    operator="equals"
                )
            ],
        ),
        create_dual_purpose_input(
            name="range_end",
            display_name="End Value",
            input_type="number",
            required=False,
            value=10,
            info="Ending number for the range (inclusive). Can be connected from another node or entered directly.",
            input_types=["number", "integer", "Any"],
            visibility_rules=[
                InputVisibilityRule(
                    field_name="iteration_source_type",
                    field_value="number_range",
                    operator="equals"
                )
            ],
            requirement_rules=[
                InputRequirementRule(
                    field_name="iteration_source_type",
                    field_value="number_range",
                    operator="equals"
                )
            ],
        ),
        create_dual_purpose_input(
            name="range_step",
            display_name="Step Size",
            input_type="number",
            required=False,
            value=1,
            info="Step size for the range (default: 1). Can be connected from another node or entered directly.",
            input_types=["number", "integer", "Any"],
            visibility_rules=[
                InputVisibilityRule(
                    field_name="iteration_source_type",
                    field_value="number_range",
                    operator="equals"
                )
            ],
        ),


        # === ITERATION SETTINGS ===
        BoolInput(
            name="parallel_execution",
            display_name="Parallel Execution",
            value=True,
            info="Execute loop iterations in parallel for better performance.",
            advanced=False,  # Core setting, not advanced
        ),
        IntInput(
            name="max_concurrent",
            display_name="Max Concurrent Iterations",
            value=3,
            min_value=1,
            max_value=20,
            info="Maximum number of iterations to run concurrently (1-20).",
            visibility_rules=[
                InputVisibilityRule(
                    field_name="parallel_execution",
                    field_value=True,
                    operator="equals"
                )
            ],
        ),
        BoolInput(
            name="preserve_order",
            display_name="Preserve Order",
            value=True,
            info="Maintain the original order of items in the results.",
            advanced=False,
        ),
        IntInput(
            name="iteration_timeout",
            display_name="Iteration Timeout (seconds)",
            value=60,
            min_value=1,
            max_value=3600,
            info="Maximum time to wait for each iteration to complete (1-3600 seconds).",
            advanced=True,
        ),

        # === RESULT AGGREGATION ===
        DropdownInput(
            name="aggregation_type",
            display_name="Result Aggregation",
            options=["collect_all", "collect_successful", "count_only", "latest_only", "first_success", "combine_text"],
            value="collect_all",
            info="How to aggregate results from all iterations.",
            advanced=False,
        ),
        BoolInput(
            name="include_metadata",
            display_name="Include Metadata",
            value=True,
            info="Include metadata (timing, iteration index, etc.) in results.",
            advanced=True,
        ),

        # === ERROR HANDLING ===
        DropdownInput(
            name="on_iteration_error",
            display_name="On Iteration Error",
            options=["continue", "retry_once", "retry_twice", "exit_loop"],
            value="continue",
            info="How to handle errors in individual iterations.",
            advanced=False,
        ),
        BoolInput(
            name="include_errors",
            display_name="Include Errors in Results",
            value=True,
            info="Include error information in the final results.",
            advanced=True,
        ),
    ]

    outputs: List[Output] = [
        Output(
            name="current_item",
            display_name="Current Order (Iteration Output)",
            output_type="object",
            info="Outputs the item for the current iteration of the loop.",
            result_path_hint="current_iteration_item",
        ),
        Output(
            name="final_results",
            display_name="All Results (Exit Output)",
            output_type="array",
            info="Outputs the aggregated results after all iterations are complete.",
            result_path_hint="aggregated_results",
        ),
    ]

    # CORRECT: Define a default instance of LoopConfig on the class.
    # This serves as the template for get_definition and for new instances.
    loop_config: LoopConfig = LoopConfig()

    @classmethod
    def get_definition(cls) -> Dict[str, Any]:
        """
        Generates the JSON-serializable definition for the frontend.
        """
        definition = super().get_definition()
        # This now works because cls.loop_config is a Pydantic model instance.
        definition["loop_config"] = cls.loop_config.model_dump()
        return definition

    def __init__(self, **data: Any):
        """
        Initializes the LoopNode instance.

        Builds the loop_config from input values, ensuring every instance gets its own copy
        and preventing shared state issues between different loop nodes in a workflow.
        """
        super().__init__(**data)

        # Build loop_config from input values in the config
        config = data.get("config", {})

        # Extract iteration settings from inputs
        iteration_settings = IterationSettings(
            parallel_execution=config.get("parallel_execution", True),
            max_concurrent=config.get("max_concurrent", 3),
            preserve_order=config.get("preserve_order", True),
            iteration_timeout=config.get("iteration_timeout", 60)
        )

        # Extract result aggregation from inputs
        result_aggregation = ResultAggregation(
            aggregation_type=config.get("aggregation_type", "collect_successful"),
            include_metadata=config.get("include_metadata", True)
        )

        # Extract error handling from inputs
        error_handling = ErrorHandling(
            on_iteration_error=config.get("on_iteration_error", "retry_once"),
            include_errors=config.get("include_errors", True)
        )

        # Extract iteration source from inputs
        source_type = config.get("iteration_source_type", "list")
        iteration_source_data = {"source_type": source_type}

        if source_type == "list":
            # Parse items from string if needed
            items_list = config.get("items_list", [])
            if isinstance(items_list, str):
                # If empty string, it means the input is connected - leave it empty for runtime resolution
                if items_list.strip() == "":
                    items_list = []
                else:
                    try:
                        import json
                        items_list = json.loads(items_list)
                    except (json.JSONDecodeError, ValueError):
                        items_list = ["item1", "item2", "item3"]  # Default fallback for invalid JSON
            iteration_source_data["static_items"] = items_list
            iteration_source_data["list_step_size"] = config.get("list_step_size", 1)
        elif source_type == "number_range":
            iteration_source_data["range_config"] = RangeConfig(
                start=config.get("range_start", 1),
                end=config.get("range_end", 10),
                step=config.get("range_step", 1)
            )

        iteration_source = IterationSource(**iteration_source_data)

        # Simple exit condition - always process all items
        exit_condition = ExitCondition(condition_type="all_items_processed")

        # Build complete loop_config
        self.loop_config = LoopConfig(
            iteration_behavior="independent",
            iteration_source=iteration_source,
            exit_condition=exit_condition,
            iteration_settings=iteration_settings,
            result_aggregation=result_aggregation,
            error_handling=error_handling
        )

    def _chunk_list(self, items: List[Any], chunk_size: int) -> List[List[Any]]:
        """
        Split a list into chunks of specified size.
        """
        if chunk_size <= 0:
            chunk_size = 1

        chunks = []
        for i in range(0, len(items), chunk_size):
            chunks.append(items[i:i + chunk_size])
        return chunks

    async def execute(self, context: WorkflowContext) -> NodeResult:
        """
        Execute the loop node.

        Note: This is a declarative node - the actual loop execution is handled
        by the orchestration engine. This method validates inputs and returns
        the loop configuration.
        """
        try:
            # Extract the loop input
            loop_input = self.get_input_value("loop_input", context)

            if not loop_input:
                return NodeResult(
                    success=False,
                    error="No input data provided for loop processing"
                )

            # Validate that we have an iterable
            if not isinstance(loop_input, (list, tuple)):
                # Try to convert string representation to list if needed
                if isinstance(loop_input, str):
                    try:
                        import json
                        loop_input = json.loads(loop_input)
                        if not isinstance(loop_input, (list, tuple)):
                            raise ValueError("Input must be an array")
                    except (json.JSONDecodeError, ValueError):
                        return NodeResult(
                            success=False,
                            error="Loop input must be an array or valid JSON array string"
                        )
                else:
                    return NodeResult(
                        success=False,
                        error="Loop input must be an array"
                    )

            # Return success with loop configuration
            # The orchestration engine will use this configuration to execute the loop
            return NodeResult(
                success=True,
                data={
                    "loop_config": self.loop_config.model_dump(),
                    "input_data": loop_input,
                    "total_items": len(loop_input)
                }
            )

        except Exception as e:
            return NodeResult(
                success=False,
                error=f"Loop node execution failed: {str(e)}"
            )

    def execute(self, **kwargs) -> Dict[str, Any]:
        """
        The execute method for LoopNode is a placeholder.

        The orchestration engine is responsible for interpreting the `loop_config`
        and managing the iteration, state, and execution flow. This method
        is not intended to be called directly for loop logic.
        """
        print(
            f"INFO ({self.name}): Execute called. Loop logic is handled by the orchestration engine, not this node."
        )
        return {}