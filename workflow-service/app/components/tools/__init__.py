"""Tools components for workflow builder.

This package contains components that provide integration with external tools and services.

NOTE: MCP Tools component is currently disabled from the frontend by setting
is_abstract = True in its class definition. To re-enable it, set
is_abstract = False in the MCPToolsComponent class.
"""

try:
    from app.components.tools.mcp_tools import MCPToolsComponent

    __all__ = ["MCPToolsComponent"]  # Currently disabled (is_abstract = True)
except ImportError as e:
    print(f"Warning: Could not import MCPToolsComponent: {e}")
    __all__ = []
