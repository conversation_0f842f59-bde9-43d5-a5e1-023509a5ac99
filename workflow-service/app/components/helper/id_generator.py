from typing import Dict, Any, List, ClassVar
import uuid
import time
import random
import string
import asyncio

from app.components.core.base_node import BaseNode
from app.models.workflow_builder.components import (
    InputBase,
    DropdownInput,
    IntInput,
    InputVisibilityRule,
)
from app.models.workflow_builder.components import Output


class IDGeneratorComponent(BaseNode):
    """
    Generates various types of unique identifiers.

    This component can generate different types of unique identifiers including:
    - UUIDv4: Standard universally unique identifier
    - Timestamp ID: Time-based identifier
    - Short ID: Configurable length alphanumeric identifier
    
    NOTE: This component is currently disabled from the frontend.
    To re-enable, set is_abstract = False.
    """

    name: ClassVar[str] = "IDGeneratorComponent"
    display_name: ClassVar[str] = "ID Generator"
    description: ClassVar[str] = (
        "Generates various types of unique identifiers (UUID, timestamp, short ID)."
    )

    category: ClassVar[str] = "Helpers"
    icon: ClassVar[str] = "Fingerprint"
    is_abstract: ClassVar[bool] = True  # Disabled from frontend - set to False to re-enable

    inputs: ClassVar[List[InputBase]] = [
        DropdownInput(
            name="id_type",
            display_name="ID Type",
            options=["UUIDv4", "Timestamp ID", "Short ID"],
            value="UUIDv4",
            info="The type of unique identifier to generate.",
        ),
        IntInput(
            name="short_id_length",
            display_name="Short ID Length",
            value=8,
            info="The length of the short ID (only used when ID Type is 'Short ID').",
            visibility_rules=[InputVisibilityRule(field_name="id_type", field_value="Short ID")],
        ),
    ]

    outputs: ClassVar[List[Output]] = [
        Output(name="unique_id", display_name="Unique ID", output_type="string"),
        Output(name="error", display_name="Error", output_type="string"),
    ]

    def build(self, **kwargs) -> Dict[str, Any]:
        """
        Legacy executor for the IDGeneratorComponent.
        Generates a unique identifier based on the selected type.

        Args:
            id_type: The type of ID to generate ("UUIDv4", "Timestamp ID", or "Short ID")
            short_id_length: The length of the short ID (only used when id_type is "Short ID")

        Returns:
            A dictionary with the generated unique ID and any error message.
        """
        print(f"Executing {self.name} using legacy build method...")
        # Get input values
        id_type = kwargs.get("id_type", "UUIDv4")
        short_id_length = kwargs.get("short_id_length", 8)

        # Initialize result
        result = {"unique_id": None, "error": None}

        try:
            # Generate ID based on type
            if id_type == "UUIDv4":
                result["unique_id"] = str(uuid.uuid4())

            elif id_type == "Timestamp ID":
                # Generate millisecond timestamp
                result["unique_id"] = str(int(time.time() * 1000))

            elif id_type == "Short ID":
                # Validate short_id_length
                if not isinstance(short_id_length, int) or short_id_length <= 0:
                    raise ValueError(
                        f"Short ID length must be a positive integer, got: {short_id_length}"
                    )

                # Generate random alphanumeric string
                chars = string.ascii_letters + string.digits
                result["unique_id"] = "".join(random.choices(chars, k=short_id_length))

            else:
                raise ValueError(f"Unknown ID type: {id_type}")

        except Exception as e:
            result["error"] = str(e)

        return result
