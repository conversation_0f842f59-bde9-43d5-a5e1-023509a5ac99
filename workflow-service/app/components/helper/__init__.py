"""
Helper components for workflow builder.

This package contains utility components that provide helper functionality
for workflows, such as ID generation, document extraction, etc.

NOTE: Helper components are currently disabled from the frontend by setting
is_abstract = True in their class definitions. To re-enable them, set
is_abstract = False in the respective component files.
"""

from app.components.helper.id_generator import IDGeneratorComponent
from app.components.helper.doc_extractor import DocExtractorComponent

__all__ = [
    "IDGeneratorComponent",  # Currently disabled (is_abstract = True)
    "DocExtractorComponent",  # Currently disabled (is_abstract = True)
]
