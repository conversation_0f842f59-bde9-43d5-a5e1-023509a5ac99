{"nodes": [{"id": "MCP_Candidate_Interview_candidate_suitability_pre", "server_script_path": "", "server_tools": [{"tool_id": 1, "tool_name": "MCP_Candidate_Interview_candidate_suitability_pre", "input_schema": {"predefined_fields": [{"field_name": "resume_s3_link", "data_type": {"type": "string", "description": "S3 link to the candidate's resume"}, "required": true}, {"field_name": "job_description_s3_link", "data_type": {"type": "string", "description": "S3 link to the job description"}, "required": true}]}, "output_schema": {"predefined_fields": [{"field_name": "suitability_analysis", "data_type": {"type": "string", "description": "Analysis of candidate's suitability for the job", "format": "string"}}, {"field_name": "resume_details", "data_type": {"type": "string", "description": "Candidate's resume", "format": "string"}}, {"field_name": "jd_details", "data_type": {"type": "string", "description": "Interview job description", "format": "string"}}]}}]}, {"id": "AgenticAI", "server_script_path": "", "server_tools": [{"tool_id": 1, "tool_name": "AgenticAI", "input_schema": {"predefined_fields": [{"field_name": "model_provider", "data_type": {"type": "string", "description": "The AI model provider to use."}, "required": false}, {"field_name": "base_url", "data_type": {"type": "string", "description": "Base URL for the API (leave empty for default provider URL)."}, "required": false}, {"field_name": "api_key", "data_type": {"type": "string", "description": "API key for the model provider. Can be entered directly or referenced from secure storage."}, "required": false}, {"field_name": "model_name", "data_type": {"type": "string", "description": "Select the model to use. The list includes models from OpenAI, Anthropic, Google, Mistral, and Ollama."}, "required": false}, {"field_name": "temperature", "data_type": {"type": "number", "description": "Controls randomness: 0 is deterministic, higher values are more random."}, "required": false}, {"field_name": "description", "data_type": {"type": "string", "description": "Description of the agent for UI display."}, "required": false}, {"field_name": "execution_type", "data_type": {"type": "string", "description": "Determines if agent handles single response or multi-turn conversation."}, "required": false}, {"field_name": "query", "data_type": {"type": "string", "description": "The task, query, or objective for the agent to accomplish. Can be connected from another node or entered directly."}, "required": true}, {"field_name": "system_message", "data_type": {"type": "string", "description": "System prompt/instructions for the agent. If empty, will use default based on query."}, "required": false}, {"field_name": "termination_condition", "data_type": {"type": "string", "description": "Defines when multi-turn conversations should end. Required for interactive execution type."}, "required": false}, {"field_name": "max_tokens", "data_type": {"type": "number", "description": "Maximum response length in tokens."}, "required": false}, {"field_name": "input_variables", "data_type": {"type": "object", "description": "Dictionary of variables to provide to the agent. Can be connected from another node or entered directly."}, "required": false}, {"field_name": "memory", "data_type": {"type": "string", "description": "Connect a memory object from another node."}, "required": false}, {"field_name": "autogen_agent_type", "data_type": {"type": "string", "description": "The type of AutoGen agent to create internally."}, "required": false}]}, "output_schema": {"predefined_fields": [{"field_name": "final_answer", "data_type": {"type": "string", "description": "", "format": "string"}}, {"field_name": "intermediate_steps", "data_type": {"type": "string", "description": "", "format": "string"}}, {"field_name": "updated_memory", "data_type": {"type": "string", "description": "", "format": "datetime"}}, {"field_name": "error", "data_type": {"type": "string", "description": "", "format": "string"}}]}}]}, {"id": "LoopNode", "server_script_path": "", "server_tools": [{"tool_id": 1, "tool_name": "LoopNode", "input_schema": {"predefined_fields": [{"field_name": "loop_input", "data_type": {"type": "string", "description": "The array of items to be processed by the loop. Can be connected from another node or entered directly."}, "required": true}, {"field_name": "parallel_execution", "data_type": {"type": "boolean", "description": "Execute loop iterations in parallel for better performance."}, "required": false}, {"field_name": "max_concurrent", "data_type": {"type": "number", "description": "Maximum number of iterations to run concurrently (1-20)."}, "required": false}, {"field_name": "preserve_order", "data_type": {"type": "boolean", "description": "Maintain the original order of items in the results."}, "required": false}, {"field_name": "iteration_timeout", "data_type": {"type": "number", "description": "Maximum time to wait for each iteration to complete (1-3600 seconds)."}, "required": false}, {"field_name": "aggregation_type", "data_type": {"type": "string", "description": "How to aggregate results from all iterations."}, "required": false}, {"field_name": "include_metadata", "data_type": {"type": "boolean", "description": "Include metadata (timing, iteration index, etc.) in results."}, "required": false}, {"field_name": "on_iteration_error", "data_type": {"type": "string", "description": "How to handle errors in individual iterations."}, "required": false}, {"field_name": "include_errors", "data_type": {"type": "boolean", "description": "Include error information in the final results."}, "required": false}, {"field_name": "input_field_path", "data_type": {"type": "string", "description": "Path to the field containing the array to iterate over (for complex input objects)."}, "required": false}]}, "output_schema": {"predefined_fields": [{"field_name": "current_item", "data_type": {"type": "object", "description": "", "format": "string"}}, {"field_name": "final_results", "data_type": {"type": "array", "description": "", "format": "string"}}]}}]}], "transitions": [{"id": "transition-LoopNode-1750768354069", "sequence": 1, "transition_type": "initial", "execution_type": "loop", "node_info": {"node_id": "LoopNode", "tools_to_use": [{"tool_id": 1, "tool_name": "LoopNode", "tool_params": {"items": [{"field_name": "loop_input", "data_type": "string", "field_value": ""}, {"field_name": "parallel_execution", "data_type": "boolean", "field_value": true}, {"field_name": "max_concurrent", "data_type": "number", "field_value": 3}, {"field_name": "preserve_order", "data_type": "boolean", "field_value": true}, {"field_name": "iteration_timeout", "data_type": "number", "field_value": 60}, {"field_name": "aggregation_type", "data_type": "string", "field_value": "collect_successful"}, {"field_name": "include_metadata", "data_type": "boolean", "field_value": true}, {"field_name": "on_iteration_error", "data_type": "string", "field_value": "retry_once"}, {"field_name": "include_errors", "data_type": "boolean", "field_value": true}, {"field_name": "input_field_path", "data_type": "string", "field_value": "orders"}]}}], "input_data": [], "output_data": [{"to_transition_id": "transition-MCP_Candidate_Interview_candidate_suitability_pre-1750765240845", "target_node_id": "0447fd55-c8f5-4c65-b2c3-e768bd663b13", "data_type": "string", "output_handle_registry": {"handle_mappings": [{"handle_id": "current_item", "result_path": "current_item", "edge_id": "reactflow__edge-LoopNode-1750768354069current_item-MCP_Candidate_Interview_candidate_suitability_pre-1750765240845resume_s3_link"}]}}, {"to_transition_id": "transition-AgenticAI-1750765267026", "target_node_id": "AI Agent Executor", "data_type": "string", "output_handle_registry": {"handle_mappings": [{"handle_id": "final_results", "result_path": "final_results", "edge_id": "reactflow__edge-LoopNode-1750768354069final_results-AgenticAI-1750765267026query"}]}}]}, "result_resolution": {"node_type": "loop", "expected_result_structure": "direct", "handle_registry": {"input_handles": [{"handle_id": "loop_input", "handle_name": "Items to Process", "data_type": "string", "required": true, "description": "The array of items to be processed by the loop. Can be connected from another node or entered directly."}], "output_handles": [{"handle_id": "current_item", "handle_name": "Current Order (Iteration Output)", "data_type": "object", "description": ""}, {"handle_id": "final_results", "handle_name": "All Results (Exit Output)", "data_type": "array", "description": ""}]}, "result_path_hints": {"current_item": "current_item", "final_results": "final_results"}, "dynamic_discovery": {"enabled": false, "fallback_patterns": ["result.current_item", "output_data.current_item", "response.current_item", "data.current_item", "result.final_results", "output_data.final_results", "response.final_results", "data.final_results", "{handle_id}", "result", "output_data", "response", "data", "result.{handle_id}", "output_data.{handle_id}", "result.result", "response.data", "content", "value"], "validation_rules": [{"rule_type": "type_check", "rule_config": {"allowed_types": ["string", "number", "object", "array", "boolean"], "reject_null": false, "reject_undefined": true}}, {"rule_type": "structure_check", "rule_config": {"min_depth": 0, "max_depth": 5, "allow_nested_objects": true, "allow_arrays": true}}, {"rule_type": "content_check", "rule_config": {"min_length": 0, "reject_empty_strings": false, "reject_empty_objects": false, "reject_empty_arrays": false}}]}, "extraction_metadata": {"supports_multiple_outputs": true, "supports_nested_results": false, "requires_dynamic_discovery": false, "primary_output_handle": "current_item"}}, "approval_required": false, "end": false}, {"id": "transition-MCP_Candidate_Interview_candidate_suitability_pre-1750765240845", "sequence": 2, "transition_type": "standard", "execution_type": "Components", "node_info": {"node_id": "MCP_Candidate_Interview_candidate_suitability_pre", "tools_to_use": [{"tool_id": 1, "tool_name": "candidate_suitability_pre", "tool_params": {"items": [{"field_name": "resume_s3_link", "data_type": "string", "field_value": null}, {"field_name": "job_description_s3_link", "data_type": "string", "field_value": "heeloo"}]}}], "input_data": [{"from_transition_id": "transition-LoopNode-1750768354069", "source_node_id": "For Each Loop", "data_type": "string", "handle_mappings": [{"source_transition_id": "transition-LoopNode-1750768354069", "source_handle_id": "current_item", "target_handle_id": "resume_s3_link", "edge_id": "reactflow__edge-LoopNode-1750768354069current_item-MCP_Candidate_Interview_candidate_suitability_pre-1750765240845resume_s3_link"}]}], "output_data": []}, "result_resolution": {"node_type": "component", "expected_result_structure": "direct", "handle_registry": {"input_handles": [{"handle_id": "resume_s3_link", "handle_name": "Resume S3 Link", "data_type": "string", "required": true, "description": "S3 link to the candidate's resume"}, {"handle_id": "job_description_s3_link", "handle_name": "Job Description S3 Link", "data_type": "string", "required": true, "description": "S3 link to the job description"}], "output_handles": [{"handle_id": "suitability_analysis", "handle_name": "suitability_analysis", "data_type": "string", "description": ""}, {"handle_id": "resume_details", "handle_name": "resume_details", "data_type": "string", "description": ""}, {"handle_id": "jd_details", "handle_name": "jd_details", "data_type": "string", "description": ""}]}, "result_path_hints": {"suitability_analysis": "suitability_analysis", "resume_details": "resume_details", "jd_details": "jd_details"}, "dynamic_discovery": {"enabled": false, "fallback_patterns": ["result.suitability_analysis", "output_data.suitability_analysis", "response.suitability_analysis", "data.suitability_analysis", "result.resume_details", "output_data.resume_details", "response.resume_details", "data.resume_details", "result.jd_details", "output_data.jd_details", "response.jd_details", "data.jd_details", "{handle_id}", "result", "output_data", "response", "data", "result.{handle_id}", "output_data.{handle_id}", "result.result", "response.data", "content", "value"], "validation_rules": [{"rule_type": "type_check", "rule_config": {"allowed_types": ["string", "number", "object", "array", "boolean"], "reject_null": false, "reject_undefined": true}}, {"rule_type": "structure_check", "rule_config": {"min_depth": 0, "max_depth": 5, "allow_nested_objects": true, "allow_arrays": true}}, {"rule_type": "content_check", "rule_config": {"min_length": 0, "reject_empty_strings": false, "reject_empty_objects": false, "reject_empty_arrays": false}}]}, "extraction_metadata": {"supports_multiple_outputs": true, "supports_nested_results": false, "requires_dynamic_discovery": false, "primary_output_handle": "suitability_analysis"}}, "approval_required": false, "end": true}, {"id": "transition-AgenticAI-1750765267026", "sequence": 3, "transition_type": "standard", "execution_type": "agent", "node_info": {"node_id": "AgenticAI", "tools_to_use": [{"tool_id": 1, "tool_name": "AgenticAI", "tool_params": {"items": [{"field_name": "agent_type", "data_type": "string", "field_value": "component"}, {"field_name": "execution_type", "data_type": "string", "field_value": "response"}, {"field_name": "query", "data_type": "string", "field_value": null}, {"field_name": "agent_config", "data_type": "object", "field_value": {"model_config": {"model_provider": "OpenAI", "model": "gpt-4o", "temperature": 0.7, "max_tokens": 1000}, "description": "", "system_message": "hello ", "autogen_agent_type": "Assistant", "termination_condition": "", "input_variables": {}}}]}}], "input_data": [{"from_transition_id": "transition-LoopNode-1750768354069", "source_node_id": "For Each Loop", "data_type": "string", "handle_mappings": [{"source_transition_id": "transition-LoopNode-1750768354069", "source_handle_id": "final_results", "target_handle_id": "query", "edge_id": "reactflow__edge-LoopNode-1750768354069final_results-AgenticAI-1750765267026query"}]}], "output_data": []}, "result_resolution": {"node_type": "agent", "expected_result_structure": "direct", "handle_registry": {"input_handles": [{"handle_id": "query", "handle_name": "Query/Objective", "data_type": "string", "required": true, "description": "The task, query, or objective for the agent to accomplish. Can be connected from another node or entered directly."}, {"handle_id": "input_variables", "handle_name": "Input Variables", "data_type": "object", "required": false, "description": "Dictionary of variables to provide to the agent. Can be connected from another node or entered directly."}, {"handle_id": "tools", "handle_name": "Tools", "data_type": "string", "required": false, "description": "Connect workflow components (including MCP marketplace components) to use as tools for the agent. Multiple tools can connect to this single handle."}, {"handle_id": "memory", "handle_name": "Memory Object", "data_type": "string", "required": false, "description": "Connect a memory object from another node."}], "output_handles": [{"handle_id": "final_answer", "handle_name": "Final Answer", "data_type": "string", "description": ""}, {"handle_id": "intermediate_steps", "handle_name": "Intermediate Steps", "data_type": "string", "description": ""}, {"handle_id": "updated_memory", "handle_name": "Updated Memory", "data_type": "string", "description": ""}, {"handle_id": "error", "handle_name": "Error", "data_type": "string", "description": ""}]}, "result_path_hints": {"final_answer": "final_answer", "intermediate_steps": "intermediate_steps", "updated_memory": "updated_memory", "error": "error"}, "dynamic_discovery": {"enabled": false, "fallback_patterns": ["result.final_answer", "output_data.final_answer", "response.final_answer", "data.final_answer", "result.intermediate_steps", "output_data.intermediate_steps", "response.intermediate_steps", "data.intermediate_steps", "result.updated_memory", "output_data.updated_memory", "response.updated_memory", "data.updated_memory", "result.error", "output_data.error", "response.error", "data.error", "{handle_id}", "result", "output_data", "response", "data", "result.{handle_id}", "output_data.{handle_id}", "result.result", "response.data", "content", "value"], "validation_rules": [{"rule_type": "type_check", "rule_config": {"allowed_types": ["string", "number", "object", "array", "boolean"], "reject_null": false, "reject_undefined": true}}, {"rule_type": "structure_check", "rule_config": {"min_depth": 0, "max_depth": 5, "allow_nested_objects": true, "allow_arrays": true}}, {"rule_type": "content_check", "rule_config": {"min_length": 0, "reject_empty_strings": false, "reject_empty_objects": false, "reject_empty_arrays": false}}]}, "extraction_metadata": {"supports_multiple_outputs": true, "supports_nested_results": false, "requires_dynamic_discovery": false, "primary_output_handle": "final_answer"}}, "approval_required": false, "end": true}]}