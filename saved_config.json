{"name": "Untitled Workflow", "description": "Untitled_Workflow", "workflow_data": {"nodes": [{"id": "start-node", "type": "WorkflowNode", "position": {"x": 100, "y": 100}, "data": {"label": "Start", "type": "component", "originalType": "StartNode", "definition": {"name": "StartNode", "display_name": "Start", "description": "The starting point for all workflows. Only nodes connected to this node will be executed.", "category": "Input/Output", "icon": "Play", "beta": false, "inputs": [], "outputs": [{"name": "flow", "display_name": "Flow", "output_type": "Any"}], "is_valid": true, "path": "components.io.start_node"}, "config": {"collected_parameters": {"LoopNode-1750768354069_loop_input": {"node_id": "LoopNode-1750768354069", "node_name": "For Each Loop", "input_name": "loop_input", "connected_to_start": true, "required": true, "input_type": "string", "options": null}}}}, "width": 208, "height": 122, "selected": false, "dragging": false}, {"id": "MCP_Candidate_Interview_candidate_suitability_pre-1750765240845", "type": "WorkflowNode", "position": {"x": 960, "y": -220}, "data": {"label": "Candidate_Interview - candidate_suitability_pre", "type": "component", "originalType": "MCP_Candidate_Interview_candidate_suitability_pre", "definition": {"name": "MCP_Candidate_Interview_candidate_suitability_pre", "display_name": "Candidate_Interview - candidate_suitability_pre", "description": "Analyze candidate suitability based on job description and resume links", "category": "MCP Marketplace", "icon": "Cloud", "beta": true, "inputs": [{"name": "resume_s3_link", "display_name": "Resume S3 Link", "info": "S3 link to the candidate's resume", "input_type": "string", "input_types": ["string", "Any"], "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {"minLength": 1, "maxLength": 2083}}, {"name": "job_description_s3_link", "display_name": "Job Description S3 Link", "info": "S3 link to the job description", "input_type": "string", "input_types": ["string", "Any"], "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "validation": {"minLength": 1, "maxLength": 2083}}], "outputs": [{"name": "suitability_analysis", "display_name": "suitability_analysis", "output_type": "string"}, {"name": "resume_details", "display_name": "resume_details", "output_type": "string"}, {"name": "jd_details", "display_name": "jd_details", "output_type": "string"}], "is_valid": true, "path": "mcp.mcp_candidate_interview_candidate_suitability_pre", "type": "MCP", "env_keys": [], "env_credential_status": "pending_input", "mcp_info": {"server_id": "0447fd55-c8f5-4c65-b2c3-e768bd663b13", "server_path": "", "tool_name": "candidate_suitability_pre", "input_schema": {"properties": {"resume_s3_link": {"description": "S3 link to the candidate's resume", "format": "uri", "maxLength": 2083, "minLength": 1, "title": "Resume S3 Link", "type": "string"}, "job_description_s3_link": {"description": "S3 link to the job description", "format": "uri", "maxLength": 2083, "minLength": 1, "title": "Job Description S3 Link", "type": "string"}}, "required": ["resume_s3_link", "job_description_s3_link"], "title": "CandidateSuitabilityPreSchema", "type": "object"}, "output_schema": {"properties": {"suitability_analysis": {"type": "string", "description": "Analysis of candidate's suitability for the job", "title": "suitability_analysis"}, "resume_details": {"type": "string", "description": "Candidate's resume", "title": "resume_details"}, "jd_details": {"type": "string", "description": "Interview job description", "title": "jd_details"}}}}}, "config": {"job_description_s3_link": "heeloo"}}, "width": 208, "height": 184, "selected": false, "dragging": false, "style": {"opacity": 1}}, {"id": "AgenticAI-1750765267026", "type": "WorkflowNode", "position": {"x": 980, "y": 380}, "data": {"label": "AI Agent Executor", "type": "agent", "originalType": "AgenticAI", "definition": {"name": "AgenticAI", "display_name": "AI Agent Executor", "description": "Executes an AI agent with tools and memory using AutoGen.", "category": "AI", "icon": "Bot", "type": "component", "beta": true, "requires_approval": false, "inputs": [{"name": "model_provider", "display_name": "Model Provider", "info": "The AI model provider to use.", "input_type": "dropdown", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "OpenAI", "options": ["OpenAI", "Azure OpenAI", "Anthropic", "<PERSON>", "Google", "Gemini", "<PERSON><PERSON><PERSON>", "Ollama", "Custom"], "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "base_url", "display_name": "Base URL", "info": "Base URL for the API (leave empty for default provider URL).", "input_type": "string", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": [{"field_name": "model_provider", "field_value": "Custom", "operator": "equals"}, {"field_name": "model_provider", "field_value": "Azure OpenAI", "operator": "equals"}, {"field_name": "model_provider", "field_value": "Ollama", "operator": "equals"}], "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "api_key", "display_name": "API Key", "info": "API key for the model provider. Can be entered directly or referenced from secure storage.", "input_type": "credential", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR", "credential_type": "api_key", "use_credential_id": false, "credential_id": ""}, {"name": "model_name", "display_name": "Model", "info": "Select the model to use. The list includes models from OpenAI, Anthropic, Google, Mistral, and Ollama.", "input_type": "dropdown", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "gpt-4o", "options": ["gpt-4o", "gpt-4o-mini", "gpt-4-turbo", "gpt-4", "gpt-3.5-turbo", "claude-3-5-sonnet-20241022", "claude-3-5-haiku-20241022", "claude-3-opus-20240229", "claude-3-sonnet-20240229", "claude-3-haiku-20240307", "claude-2.1", "claude-2.0", "gemini-1.5-pro", "gemini-1.5-flash", "gemini-pro", "gemini-pro-vision", "mistral-large-latest", "mistral-medium-latest", "mistral-small-latest", "open-mistral-7b", "open-mixtral-8x7b", "open-mixtral-8x22b", "llama3.2", "llama3.1", "llama3", "llama2", "mistral", "mixtral", "phi3", "gemma", "codellama", "qwen2"], "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "temperature", "display_name": "Temperature", "info": "Controls randomness: 0 is deterministic, higher values are more random.", "input_type": "float", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": 0.7, "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "description", "display_name": "Description", "info": "Description of the agent for UI display.", "input_type": "string", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "execution_type", "display_name": "Execution Type", "info": "Determines if agent handles single response or multi-turn conversation.", "input_type": "dropdown", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "response", "options": ["response", "interactive"], "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "query", "display_name": "Query/Objective", "info": "The task, query, or objective for the agent to accomplish. Can be connected from another node or entered directly.", "input_type": "string", "input_types": ["string", "Any"], "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "system_message", "display_name": "System Message", "info": "System prompt/instructions for the agent. If empty, will use default based on query.", "input_type": "string", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "termination_condition", "display_name": "Termination Condition", "info": "Defines when multi-turn conversations should end. Required for interactive execution type.", "input_type": "string", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": [{"field_name": "execution_type", "field_value": "interactive", "operator": "equals"}], "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "max_tokens", "display_name": "<PERSON>", "info": "Maximum response length in tokens.", "input_type": "int", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": 1000, "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "input_variables", "display_name": "Input Variables", "info": "Dictionary of variables to provide to the agent. Can be connected from another node or entered directly.", "input_type": "dict", "input_types": ["dict", "Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": {}, "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "tools", "display_name": "Tools", "info": "Connect workflow components (including MCP marketplace components) to use as tools for the agent. Multiple tools can connect to this single handle.", "input_type": "handle", "input_types": ["Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "memory", "display_name": "Memory Object", "info": "Connect a memory object from another node.", "input_type": "handle", "input_types": ["Any"], "required": false, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": null, "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "autogen_agent_type", "display_name": "AutoGen Agent Type", "info": "The type of AutoGen agent to create internally.", "input_type": "dropdown", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": true, "value": "Assistant", "options": ["Assistant", "UserProxy", "CodeExecutor"], "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}], "outputs": [{"name": "final_answer", "display_name": "Final Answer", "output_type": "string", "semantic_type": null, "method": null}, {"name": "intermediate_steps", "display_name": "Intermediate Steps", "output_type": "list", "semantic_type": null, "method": null}, {"name": "updated_memory", "display_name": "Updated Memory", "output_type": "Memory", "semantic_type": null, "method": null}, {"name": "error", "display_name": "Error", "output_type": "str", "semantic_type": null, "method": null}], "is_valid": true, "path": "components.ai.agenticai", "interface_issues": []}, "config": {"id": "AgenticAI-1750765267026", "name": "AI Agent Executor", "model_provider": "OpenAI", "base_url": "", "api_key": "", "model_name": "gpt-4o", "temperature": 0.7, "description": "", "execution_type": "response", "query": "", "system_message": "hello ", "termination_condition": "", "max_tokens": 1000, "input_variables": {}, "autogen_agent_type": "Assistant", "tools": []}}, "width": 208, "height": 228, "selected": false, "dragging": false, "style": {"opacity": 1}}, {"id": "LoopNode-1750768354069", "type": "WorkflowNode", "position": {"x": 620, "y": 60}, "data": {"label": "For Each Loop", "type": "component", "originalType": "LoopNode", "definition": {"name": "LoopNode", "display_name": "For Each Loop", "description": "Iterates over a list, with advanced controls for parallelism, aggregation, and error handling.", "category": "Logic", "icon": "Repeat", "type": "component", "beta": false, "requires_approval": false, "inputs": [{"name": "loop_input", "display_name": "Items to Process", "info": "The array of items to be processed by the loop. Can be connected from another node or entered directly.", "input_type": "string", "input_types": ["array", "list", "Any"], "required": true, "is_handle": true, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "", "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "parallel_execution", "display_name": "Parallel Execution", "info": "Execute loop iterations in parallel for better performance.", "input_type": "bool", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": true, "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "max_concurrent", "display_name": "Max Concurrent Iterations", "info": "Maximum number of iterations to run concurrently (1-20).", "input_type": "int", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": 3, "options": null, "visibility_rules": [{"field_name": "parallel_execution", "field_value": "True", "operator": "equals"}], "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "preserve_order", "display_name": "Preserve Order", "info": "Maintain the original order of items in the results.", "input_type": "bool", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": true, "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "iteration_timeout", "display_name": "Iteration Timeout (seconds)", "info": "Maximum time to wait for each iteration to complete (1-3600 seconds).", "input_type": "int", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": true, "value": 60, "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "aggregation_type", "display_name": "Result Aggregation", "info": "How to aggregate results from all iterations.", "input_type": "dropdown", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "collect_successful", "options": ["collect_successful", "collect_all", "first_success", "last_success"], "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "include_metadata", "display_name": "Include Metadata", "info": "Include metadata (timing, iteration index, etc.) in results.", "input_type": "bool", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": true, "value": true, "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "on_iteration_error", "display_name": "On Iteration Error", "info": "How to handle errors in individual iterations.", "input_type": "dropdown", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": false, "value": "retry_once", "options": ["retry_once", "fail_fast", "continue", "skip"], "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "include_errors", "display_name": "Include Errors in Results", "info": "Include error information in the final results.", "input_type": "bool", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": true, "value": true, "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}, {"name": "input_field_path", "display_name": "Input Field Path", "info": "Path to the field containing the array to iterate over (for complex input objects).", "input_type": "string", "input_types": null, "required": false, "is_handle": false, "is_list": false, "real_time_refresh": false, "advanced": true, "value": "orders", "options": null, "visibility_rules": null, "visibility_logic": "OR", "requirement_rules": null, "requirement_logic": "OR"}], "outputs": [{"name": "current_item", "display_name": "Current Order (Iteration Output)", "output_type": "object", "semantic_type": null, "method": null}, {"name": "final_results", "display_name": "All Results (Exit Output)", "output_type": "array", "semantic_type": null, "method": null}], "is_valid": true, "path": "components.logic.loopnode", "interface_issues": []}, "config": {"loop_input": "", "parallel_execution": true, "max_concurrent": 3, "preserve_order": true, "iteration_timeout": 60, "aggregation_type": "collect_successful", "include_metadata": true, "on_iteration_error": "retry_once", "include_errors": true, "input_field_path": "orders"}}, "style": {"opacity": 1}, "width": 208, "height": 184, "selected": false, "positionAbsolute": {"x": 620, "y": 60}, "dragging": false}], "edges": [{"animated": true, "style": {"strokeWidth": 2, "stroke": "var(--primary)", "zIndex": 5}, "source": "start-node", "sourceHandle": "flow", "target": "LoopNode-1750768354069", "targetHandle": "loop_input", "type": "default", "id": "reactflow__edge-start-nodeflow-LoopNode-1750768354069loop_input"}, {"animated": true, "style": {"strokeWidth": 2, "stroke": "var(--primary)", "zIndex": 5}, "source": "LoopNode-1750768354069", "sourceHandle": "current_item", "target": "MCP_Candidate_Interview_candidate_suitability_pre-1750765240845", "targetHandle": "resume_s3_link", "type": "default", "id": "reactflow__edge-LoopNode-1750768354069current_item-MCP_Candidate_Interview_candidate_suitability_pre-1750765240845resume_s3_link"}, {"animated": true, "style": {"strokeWidth": 2, "stroke": "var(--primary)", "zIndex": 5}, "source": "LoopNode-1750768354069", "sourceHandle": "final_results", "target": "AgenticAI-1750765267026", "targetHandle": "query", "type": "default", "id": "reactflow__edge-LoopNode-1750768354069final_results-AgenticAI-1750765267026query"}]}, "start_node_data": [{"field": "loop_input", "type": "string", "transition_id": "transition-LoopNode-1750768354069"}]}