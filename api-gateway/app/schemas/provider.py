from typing import Optional, List
from pydantic import BaseModel, Field, HttpUrl
from datetime import datetime


# Provider Schemas
class ProviderBase(BaseModel):
    provider: str = Field(..., description="Provider name (e.g., OpenAI)", min_length=1, max_length=255)
    description: Optional[str] = Field(None, description="Optional description of the provider")
    base_url: HttpUrl = Field(..., description="Base API URL for the provider")
    is_active: Optional[bool] = Field(True, description="Whether the provider is active")
    is_default: Optional[bool] = Field(False, description="Whether this is the default provider")


class ProviderCreate(ProviderBase):
    pass


class ProviderUpdate(BaseModel):
    provider: Optional[str] = Field(None, description="Provider name", min_length=1, max_length=255)
    description: Optional[str] = Field(None, description="Description of the provider")
    base_url: Optional[HttpUrl] = Field(None, description="Base API URL for the provider")
    is_active: Optional[bool] = Field(None, description="Whether the provider is active")
    is_default: Optional[bool] = Field(None, description="Whether this is the default provider")


class ProviderInfo(BaseModel):
    id: str
    provider: str
    description: Optional[str]
    base_url: str
    is_active: bool
    is_default: bool
    created_at: datetime
    updated_at: datetime
    model_count: int = Field(0, description="Number of models for this provider")


class ProviderResponse(BaseModel):
    success: bool
    message: str
    provider: Optional[ProviderInfo] = None


class ProviderListResponse(BaseModel):
    success: bool
    message: str
    providers: List[ProviderInfo]
    pagination: "PaginationInfo"


class ProviderDeleteResponse(BaseModel):
    success: bool
    message: str


# Model Schemas
class ModelBase(BaseModel):
    provider_id: str = Field(..., description="ID of the provider this model belongs to")
    model: str = Field(..., description="Model name (e.g., gpt-4)", min_length=1, max_length=255)
    model_id: str = Field(..., description="API reference model ID", min_length=1, max_length=255)
    description: Optional[str] = Field(None, description="Optional description of the model")
    price_per_tokens: Optional[float] = Field(None, description="Pricing per tokens", ge=0)
    max_tokens: Optional[int] = Field(None, description="Maximum token limit", ge=1)
    temperature: Optional[float] = Field(0.7, description="Model temperature", ge=0, le=2)
    provider_type: Optional[str] = Field("chat", description="Provider type (e.g., chat, completion)")
    is_active: Optional[bool] = Field(True, description="Whether the model is active")
    is_default: Optional[bool] = Field(False, description="Whether this is the default model for the provider")


class ModelCreate(ModelBase):
    pass


class ModelUpdate(BaseModel):
    provider_id: Optional[str] = Field(None, description="ID of the provider this model belongs to")
    model: Optional[str] = Field(None, description="Model name", min_length=1, max_length=255)
    model_id: Optional[str] = Field(None, description="API reference model ID", min_length=1, max_length=255)
    description: Optional[str] = Field(None, description="Description of the model")
    price_per_tokens: Optional[float] = Field(None, description="Pricing per tokens", ge=0)
    max_tokens: Optional[int] = Field(None, description="Maximum token limit", ge=1)
    temperature: Optional[float] = Field(None, description="Model temperature", ge=0, le=2)
    provider_type: Optional[str] = Field(None, description="Provider type")
    is_active: Optional[bool] = Field(None, description="Whether the model is active")
    is_default: Optional[bool] = Field(None, description="Whether this is the default model for the provider")


class ModelInfo(BaseModel):
    id: str
    provider_id: str
    model: str
    model_id: str
    description: Optional[str]
    price_per_tokens: Optional[float]
    max_tokens: Optional[int]
    temperature: float
    provider_type: str
    is_active: bool
    is_default: bool
    created_at: datetime
    updated_at: datetime
    provider: ProviderInfo


class ModelResponse(BaseModel):
    success: bool
    message: str
    model: Optional[ModelInfo] = None


class ModelListResponse(BaseModel):
    success: bool
    message: str
    models: List[ModelInfo]
    pagination: "PaginationInfo"


class ModelDeleteResponse(BaseModel):
    success: bool
    message: str


# Common Schemas
class PaginationInfo(BaseModel):
    current_page: int = Field(..., description="Current page number")
    total_pages: int = Field(..., description="Total number of pages")
    total_items: int = Field(..., description="Total number of items")
    page_size: int = Field(..., description="Number of items per page")


class ListRequest(BaseModel):
    page: int = Field(1, description="Page number", ge=1)
    page_size: int = Field(10, description="Number of items per page", ge=1, le=100)
    is_active: Optional[bool] = Field(None, description="Filter by active status")


# Update forward references
ProviderListResponse.model_rebuild()
ModelListResponse.model_rebuild()