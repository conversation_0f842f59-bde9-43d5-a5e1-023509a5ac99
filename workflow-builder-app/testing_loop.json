{"id": "process_orders_loop", "sequence": 10, "transition_type": "standard", "execution_type": "loop", "node_info": {"node_id": "order_loop_node", "input_data": [{"from_transition_id": "fetch_orders", "source_node_id": "order_fetch_node", "data_type": "array", "handle_mappings": [{"source_transition_id": "fetch_orders", "source_handle_id": "orders_output", "target_handle_id": "loop_input", "edge_id": "edge_fetch_to_loop"}]}], "output_data": [{"to_transition_id": "process_single_order", "target_node_id": "order_processor_node", "data_type": "object", "output_handle_registry": {"handle_mappings": [{"handle_id": "current_item", "result_path": "current_iteration_item", "edge_id": "edge_loop_to_processor"}]}}, {"to_transition_id": "send_summary_report", "target_node_id": "reporting_node", "data_type": "array", "output_handle_registry": {"handle_mappings": [{"handle_id": "final_results", "result_path": "aggregated_results", "edge_id": "edge_loop_to_summary"}]}}]}, "result_resolution": {"node_type": "loop", "expected_result_structure": "nested_result", "handle_registry": {"input_handles": [{"handle_id": "loop_input", "handle_name": "Orders to Process", "data_type": "array", "required": true}], "output_handles": [{"handle_id": "current_item", "handle_name": "Current Order (Iteration Output)", "data_type": "object", "result_path_hint": "current_iteration_item"}, {"handle_id": "final_results", "handle_name": "All Results (Exit Output)", "data_type": "array", "result_path_hint": "aggregated_results"}]}, "dynamic_discovery": {"enabled": true, "fallback_patterns": ["result", "current_item", "aggregated_results"]}}, "loop_config": {"iteration_behavior": "independent", "iteration_source": {"source_type": "input_field", "input_field_path": "orders"}, "exit_condition": {"condition_type": "all_items_processed"}, "iteration_settings": {"parallel_execution": true, "max_concurrent": 3, "preserve_order": true, "iteration_timeout": 60}, "result_aggregation": {"aggregation_type": "collect_successful", "include_metadata": true}, "error_handling": {"on_iteration_error": "retry_once", "include_errors": true}}, "end": false}