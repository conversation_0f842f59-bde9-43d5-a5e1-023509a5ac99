import React, { memo, useMemo } from "react";
import { <PERSON>le, Position, NodeProps, useReactFlow, Node, Edge } from "reactflow";
import { WorkflowNodeData } from "@/types";
import { Card, CardHeader, CardTitle, CardDescription, CardContent } from "@/components/ui/card";
import { <PERSON><PERSON><PERSON>, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { Badge } from "@/components/ui/badge";
import { useAgenticAIToolConnections, useIsConnectedAsTool } from "@/hooks/useToolConnections";
import { generateToolStyleClasses, isToolHandle } from "@/utils/toolConnectionUtils";
import "@/styles/components/tool-connections.css";
// Import icons based on node type
import {
  LucideIcon,
  FileText,
  Database,
  Code,
  Workflow,
  Cog,
  Cpu,
  ArrowRightLeft,
  LogIn,
  LogOut,
  ShieldCheck,
} from "lucide-react";

// Helper function to get appropriate icon based on node category or type
const getNodeIcon = (category: string, type: string, originalType?: string): LucideIcon => {
  // Special case for StartNode
  if (originalType === "StartNode") {
    return LogIn;
  }

  // Map categories to icons
  switch (category?.toLowerCase()) {
    case "io":
      return type.includes("input") ? LogIn : LogOut;
    case "data":
      return Database;
    case "processing":
      return Cpu;
    case "api":
      return ArrowRightLeft;
    case "control flow":
      return Workflow;
    case "text":
      return FileText;
    case "code":
      return Code;
    default:
      return Cog; // Default icon
  }
};

// Memoize the component for performance
const WorkflowNode = memo(({ data, isConnectable, selected, id }: NodeProps<WorkflowNodeData>) => {
  const { label, definition, type, originalType } = data;

  // Get React Flow instance to access edges and nodes with error handling
  let edges: Edge[] = [];
  let nodes: Node<WorkflowNodeData>[] = [];
  let toolConnectionState: any = {
    hasConnectedTools: false,
    connectedToolCount: 0,
    toolConnections: [],
    availableToolSlots: [],
  };
  let isConnectedAsTool = false;
  let toolStyleClasses = {
    agenticAIWithTools: "",
    connectedAsTool: "",
    toolCountBadge: "",
  };

  try {
    const { getEdges, getNodes } = useReactFlow();
    edges = getEdges();
    nodes = getNodes();

    // Tool connection hooks
    toolConnectionState = useAgenticAIToolConnections(id || "", edges, nodes);
    isConnectedAsTool = useIsConnectedAsTool(id || "", edges);

    // Generate tool style classes
    toolStyleClasses = generateToolStyleClasses(
      toolConnectionState.hasConnectedTools,
      toolConnectionState.connectedToolCount,
      isConnectedAsTool
    );
  } catch (error) {
    console.warn("[WorkflowNode] Error accessing React Flow context or tool connections:", error);
    // Continue with default values
  }

  // Basic validation
  if (!definition) {
    return (
      <div className="bg-destructive/20 text-destructive rounded border p-2 text-xs font-medium">
        Missing Definition!
      </div>
    );
  }

  // Generate single tool handle for AgenticAI components
  const dynamicToolHandles = useMemo(() => {
    if (originalType !== "AgenticAI") return [];

    // Find the tools HandleInput (new simplified approach)
    const toolsInput = (definition.inputs || []).find(
      inp => inp.is_handle && inp.name === "tools"
    );

    if (!toolsInput) return [];

    // Use the existing tools handle from the component definition
    const toolHandles = [{
      name: "tools",
      display_name: "Tools",
      input_types: ["Any"],
      is_handle: true,
      input_type: "handle",
      info: "Connect workflow components to use as agent tools. Multiple tools can connect to this single handle.",
      required: false,
      is_list: false,
      real_time_refresh: false,
      advanced: false,
      value: null,
      options: null,
      visibility_rules: null,
      visibility_logic: "OR" as const,
      requirement_rules: null,
      requirement_logic: "OR" as const
    }];

    console.log(`[WorkflowNode] Generated single tool handle for AgenticAI node ${id}:`, toolHandles);

    return toolHandles;
  }, [originalType, definition.inputs, id]);

  // Filter inputs to get ONLY handles and apply visibility rules
  const handleInputs = (definition.inputs || []).filter((inp) => {
    // Only include handle inputs
    if (!inp.is_handle) return false;

    // If no visibility rules, always show
    if (!inp.visibility_rules || inp.visibility_rules.length === 0) return true;

    // Special handling for dynamic inputs
    if (
      inp.input_type === "dynamic_handle" ||
      (type === "DynamicCombineTextComponent" &&
        inp.name.startsWith("input_") &&
        inp.name.endsWith("_handle")) ||
      (originalType === "ConditionalNode" &&
        (inp.name === "primary" ||
          (inp.name.startsWith("condition_") && inp.name.match(/^condition_\d+$/))))
    ) {
      // For DynamicHandleInput (other than workflow_components)
      if (inp.input_type === "dynamic_handle") {
        const numHandles = data.config?.num_handles || inp.default_handles || 2;
        const baseNameMatch = inp.name.match(new RegExp(`${inp.base_name}_(\d+)`));
        if (baseNameMatch && baseNameMatch[1]) {
          const handleIndex = parseInt(baseNameMatch[1], 10);
          return handleIndex <= numHandles;
        }
        return true; // Show the base handle
      }

      // For DynamicCombineTextComponent (legacy support)
      if (
        type === "DynamicCombineTextComponent" &&
        inp.name.startsWith("input_") &&
        inp.name.endsWith("_handle")
      ) {
        // Extract the index from the handle name (e.g., "input_3_handle" -> 3)
        const match = inp.name.match(/input_(\d+)_handle/);
        if (match && match[1]) {
          const inputIndex = parseInt(match[1], 10);
          const numAdditionalInputs = parseInt(data.config?.num_additional_inputs || "0", 10);

          // Show the handle if its index is less than or equal to the number of additional inputs
          return inputIndex <= numAdditionalInputs;
        }
      }

      // UPDATED: For ConditionalNode input handles (new dual-purpose naming)
      if (originalType === "ConditionalNode") {
        // Handle "primary" input (condition 1)
        if (inp.name === "primary") {
          return true; // Always show primary input
        }

        // Handle "condition_X" inputs (conditions 2+)
        if (inp.name.startsWith("condition_") && inp.name.match(/^condition_\d+$/)) {
          const match = inp.name.match(/condition_(\d+)/);
          if (match && match[1]) {
            const conditionIndex = parseInt(match[1], 10);
            const numAdditionalConditions = parseInt(
              data.config?.num_additional_conditions || "0",
              10,
            );
            const totalConditions = 1 + numAdditionalConditions; // Base 1 + additional

            // Show the handle if the condition index is within the total conditions
            return conditionIndex <= totalConditions;
          }
        }
      }
    }

    // For other components (non-ConditionalNode), check each rule - if ANY rule passes, show the input
    // Note: ConditionalNode inputs should have been handled above, so this is a fallback
    if (originalType === "ConditionalNode") {
      console.warn(
        `[VISIBILITY] ConditionalNode input ${inp.name} fell through to generic rule evaluation`,
      );
    }

    return inp.visibility_rules.some((rule) => {
      // Get the target field value from config
      const targetValue = data.config?.[rule.field_name];

      // Simple equality check - show if the field_name has the specified field_value
      return targetValue === rule.field_value;
    });
  }); // No need to concat dynamic tool handles as they're now in the component definition

  // Outputs are always handles in this model
  // Special handling for ConditionalNode dynamic outputs
  let handleOutputs = definition.outputs || [];

  if (originalType === "ConditionalNode") {
    const numAdditionalConditions = parseInt(data.config?.num_additional_conditions || "0", 10);
    const totalConditions = 1 + numAdditionalConditions; // ✅ FIXED: Base 1 + additional (matches backend BASE_CONDITIONS = 1)

    // Generate dynamic outputs for conditions
    const dynamicOutputs = [];
    for (let i = 1; i <= totalConditions; i++) {
      dynamicOutputs.push({
        name: `condition_${i}`, // ✅ FIXED: Remove "_output" suffix to match backend naming
        display_name: `Condition ${i}`,
        output_type: "Any",
        info: `Outputs data when condition ${i} matches`,
      });
    }

    // Add default output
    dynamicOutputs.push({
      name: "default", // ✅ FIXED: Remove "_output" suffix to match backend naming
      display_name: "Default",
      output_type: "Any",
      info: "Outputs data when no conditions match",
    });

    handleOutputs = dynamicOutputs;
  }

  // Get the appropriate icon
  const NodeIcon = getNodeIcon(definition.category, type, originalType);

  // Determine if this is a StartNode
  const isStartNode = originalType === "StartNode";

  // Check if this node requires approval
  const requiresApproval = data.definition?.requires_approval === true;

  return (
    <TooltipProvider delayDuration={150}>
      <Card
        data-testid={originalType === "AgenticAI" ? "agentic-ai-node" : "workflow-node"}
        className={`workflow-node relative w-52 overflow-visible transition-all duration-200 ${selected ? "ring-primary scale-105 shadow-lg ring-2" : "shadow-md hover:shadow-lg"} ${isStartNode ? "border-primary bg-primary/5 border-2" : ""} ${requiresApproval ? "border-2 border-[#3F72AF]/50" : ""} ${toolStyleClasses.agenticAIWithTools} ${toolStyleClasses.connectedAsTool}`}
        style={{
          minHeight: "90px",
          height: "auto", // Allow the card to grow based on content
          zIndex: 10, // Ensure node is above connections but below tooltips
          gap: 0,
        }}
      >
        {/* Tool count badge for AgenticAI nodes with connected tools */}
        {originalType === "AgenticAI" && toolConnectionState.hasConnectedTools && (
          <div
            data-testid="tool-count-badge"
            className={toolStyleClasses.toolCountBadge}
            aria-label={`${toolConnectionState.connectedToolCount} tools connected`}
          >
            {toolConnectionState.connectedToolCount}
          </div>
        )}
        <CardHeader className="flex flex-row items-center gap-1.5 space-y-0 px-2 py-2">
          <div className="bg-primary/10 rounded-md p-1">
            <NodeIcon className="text-primary h-3.5 w-3.5" />
          </div>
          <div className="flex-1 overflow-hidden">
            <CardTitle className="truncate text-xs leading-tight font-medium">
              {label}
              {requiresApproval && (
                <Tooltip>
                  <TooltipTrigger asChild>
                    <span className="ml-1 inline-flex">
                      <ShieldCheck className="h-3 w-3 text-[#3F72AF]" />
                    </span>
                  </TooltipTrigger>
                  <TooltipContent side="top" className="p-2 text-xs">
                    <div className="font-medium">Requires Approval</div>
                    <div className="text-muted-foreground text-[10px]">
                      This node requires approval before execution
                    </div>
                  </TooltipContent>
                </Tooltip>
              )}
            </CardTitle>
            <CardDescription className="truncate text-[9px] leading-tight">
              {definition.display_name}
            </CardDescription>
          </div>
        </CardHeader>

        <CardContent className="flex flex-col gap-2 py-2 text-xs">
          {/* <div className="flex items-center justify-between">
            <Badge variant="outline" className="bg-muted/50 h-5 px-1.5 py-0 text-[9px]">
              {definition.category}
            </Badge>

          </div> */}

          {/* Approval badge */}
          {requiresApproval && (
            <Badge
              variant="warning"
              className="flex h-5 w-fit items-center gap-1 px-1.5 py-0 text-[9px]"
            >
              <ShieldCheck className="h-3 w-3" />
              Requires Approval
            </Badge>
          )}

          {/* Input and Output Handles side by side */}
          <div className="mt-2 flex gap-[10px]">
            {/* Input Handles (Left) with Tooltips */}
            <div className="flex-1">
              {handleInputs.map((input, index) => (
                <div key={`input-${input.name}`} className="relative mb-2">
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <div className="group flex items-center">
                        <Handle
                          type="target"
                          position={Position.Left}
                          id={input.name}
                          data-testid={`handle-${input.name}`}
                          data-tool-handle={isToolHandle(input.name)}
                          className={`transition-all duration-200 ${
                            isToolHandle(input.name)
                              ? "tool-handle"
                              : "regular-handle !bg-primary hover:!bg-primary/80"
                          }`}
                          style={{
                            width: "10px",
                            height: "10px",
                            borderRadius: "5px",
                            border: "2px solid var(--background)",
                            left: "-5px",
                            zIndex: 50, // Ensure handle is above other elements
                          }}
                          isConnectable={isConnectable}
                          tabIndex={isToolHandle(input.name) ? 0 : -1}
                        />
                        {/* Input label */}
                        <div
                          className="overflow-hidden rounded-[5px] border border-[#3F3F46] px-[10px] py-[3px] text-[9px] font-medium text-white"
                          style={{
                            backgroundColor: "#1B1B1B",
                          }}
                        >
                          {input.display_name}
                        </div>
                      </div>
                    </TooltipTrigger>
                    <TooltipContent
                      side="left"
                      className="bg-popover/95 z-50 p-2 text-xs backdrop-blur-sm"
                    >
                      <div className="font-medium">{input.display_name}</div>
                      <div className="text-muted-foreground text-[10px]">
                        Type: {input.input_types?.join(", ") || "any"}
                      </div>
                    </TooltipContent>
                  </Tooltip>
                </div>
              ))}
            </div>

            {/* Output Handles (Right) with Tooltips */}
            <div className="flex-1">
              {handleOutputs.map((output, index) => (
                <div key={`output-${output.name}`} className="relative mb-2">
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <div className="group flex items-center justify-end">
                        {/* Output label */}
                        <div
                          className="overflow-hidden rounded-[5px] border border-[#3F3F46] px-[10px] py-[3px] text-[9px] font-medium text-white"
                          style={{
                            backgroundColor: "#1B1B1B",
                          }}
                        >
                          {output.display_name}
                        </div>
                        <Handle
                          type="source"
                          position={Position.Right}
                          id={output.name}
                          data-testid={`handle-${output.name}`}
                          className="regular-handle !bg-primary hover:!bg-primary/80 transition-all duration-200"
                          style={{
                            width: "10px",
                            height: "10px",
                            borderRadius: "5px",
                            border: "2px solid var(--background)",
                            right: "-5px",
                            zIndex: 50, // Ensure handle is above other elements
                          }}
                          isConnectable={isConnectable}
                        />
                      </div>
                    </TooltipTrigger>
                    <TooltipContent
                      side="right"
                      className="bg-popover/95 z-50 p-2 text-xs backdrop-blur-sm"
                    >
                      <div className="font-medium">{output.display_name}</div>
                      <div className="text-muted-foreground text-[10px]">
                        Type: {output.output_type}
                      </div>
                    </TooltipContent>
                  </Tooltip>
                </div>
              ))}
            </div>
          </div>
        </CardContent>
      </Card>
    </TooltipProvider>
  );
});

// Add display name for React DevTools
WorkflowNode.displayName = "WorkflowNode";

export default WorkflowNode;
