import React, { use<PERSON><PERSON>back, useEffect, useRef, useState } from "react";
import {
  <PERSON><PERSON>,
  Di<PERSON><PERSON>ontent,
  Di<PERSON>Header,
  <PERSON><PERSON>Title,
  DialogFooter,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { CustomScrollArea } from "@/components/ui/custom-scroll-area";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent } from "@/components/ui/card";
import { Tabs, TabsList, TabsTrigger, TabsContent } from "@/components/ui/tabs";
import {
  Download,
  Play,
  AlertCircle,
  CheckCircle,
  Terminal,
  FileText,
  Square,
  Code,
} from "lucide-react";
import { LogDisplay } from "./LogDisplay";
import { ApprovalRequest } from "./ApprovalRequest";
import { useExecutionStore } from "@/store/executionStore";
import { executeWorkflowWithUserInputs, WorkflowExecuteWithUserInputsPayload } from "@/lib/api";
import { SSEClient } from "@/lib/sseClient";
import { shouldIncludeField, logFieldStatus } from "@/lib/field-utils";
import { clearApprovalEvent, clearAllApprovalEvents } from "@/lib/approvalUtils";
import { isNodeConnectedAsTool } from "@/utils/toolConnectionUtils";

interface ExecutionDialogProps {
  onClose: () => void;
  onStopExecution?: () => void;
  workflowId?: string;
}

export function ExecutionDialog({ onClose, onStopExecution, workflowId }: ExecutionDialogProps) {
  // Get state from Zustand store
  const {
    isDialogOpen,
    missingFields,
    // setMissingFields is no longer used since we're not adding fields from StartNode parameters
    activeTab,
    setActiveTab,
    fieldValues,
    setFieldValues,
    updateFieldValue,
    errors,
    setErrors,
    isFormValid,
    setIsFormValid,
    logs,
    addLog,
    isExecuting,
    setIsExecuting,
    isStreaming,
    setIsStreaming,
    correlationId,
    setCorrelationId,
  } = useExecutionStore();

  // Ref for auto-scrolling logs
  const logsEndRef = useRef<HTMLDivElement>(null);

  // Ref for SSE client
  const sseClientRef = useRef<SSEClient | null>(null);

  // Validate form - memoized to avoid dependency issues
  const validateForm = useCallback(
    (values: Record<string, any>, errors: Record<string, string>) => {
      const timestamp = new Date().toISOString().replace("T", " ").substring(0, 19);
      console.log(`[${timestamp}] [validateForm] ========== VALIDATING FORM ==========`);
      console.log(
        `[${timestamp}] [validateForm] Total fields to validate: ${missingFields.length}`,
      );
      console.log(
        `[${timestamp}] [validateForm] Total values provided: ${Object.keys(values).length}`,
      );
      console.log(`[${timestamp}] [validateForm] Total errors: ${Object.keys(errors).length}`);

      // Check if all required fields have values and no errors
      let allFieldsValid = true;
      let requiredFieldsCount = 0;
      let validFieldsCount = 0;

      // First pass: count required fields and check if all fields are valid
      missingFields.forEach((field) => {
        const fieldId = `${field.nodeId}_${field.name}`;
        // For boolean fields, consider them as having a value even if false
        const hasValue =
          field.inputType === "boolean" ? values[fieldId] !== undefined : !!values[fieldId];
        const hasError = !!errors[fieldId];
        // Consider fields required unless explicitly marked as optional
        const isRequired = field.required !== false;

        console.log(`[${timestamp}] [validateForm] Checking field: ${fieldId}`);
        console.log(`[${timestamp}] [validateForm] - Field type: ${field.inputType}`);
        console.log(
          `[${timestamp}] [validateForm] - Is required: ${isRequired ? "YES" : "NO"} (required !== false: ${field.required !== false})`,
        );
        console.log(`[${timestamp}] [validateForm] - Has value: ${hasValue ? "YES" : "NO"}`);
        console.log(
          `[${timestamp}] [validateForm] - Current value: ${JSON.stringify(values[fieldId])}`,
        );
        console.log(`[${timestamp}] [validateForm] - Has error: ${hasError ? "YES" : "NO"}`);
        if (hasError) {
          console.log(`[${timestamp}] [validateForm] - Error message: ${errors[fieldId]}`);
        }

        if (isRequired) {
          requiredFieldsCount++;
        }

        // Field is valid if:
        // 1. It's not required, OR
        // 2. It has a value AND no errors
        const isFieldValid = !isRequired || (hasValue && !hasError);
        console.log(
          `[${timestamp}] [validateForm] - Field is valid: ${isFieldValid ? "YES" : "NO"}`,
        );

        if (isFieldValid) {
          validFieldsCount++;
        } else {
          allFieldsValid = false;
        }
      });

      console.log(
        `[${timestamp}] [validateForm] Required fields: ${requiredFieldsCount}, Valid fields: ${validFieldsCount}`,
      );
      console.log(
        `[${timestamp}] [validateForm] Form validation result: ${allFieldsValid ? "VALID" : "INVALID"}`,
      );

      // Force immediate update of isFormValid state
      setIsFormValid(allFieldsValid);

      // Return the validation result so it can be used immediately
      return allFieldsValid;
    },
    [missingFields, setIsFormValid],
  );

  // Ref to track if the dialog has been initialized
  const hasInitializedRef = useRef(false);

  // Initialize form values when dialog opens - runs only once when dialog opens
  useEffect(() => {
    // Only run this effect when the dialog is first opened
    // This prevents infinite loops by not re-running when fieldValues or errors change
    if (!isDialogOpen) {
      // Reset the initialization flag when dialog closes
      hasInitializedRef.current = false;
      return;
    }

    // If we've already initialized for this dialog session, skip
    if (hasInitializedRef.current) return;

    // Mark that we've initialized for this dialog session
    hasInitializedRef.current = true;

    // Clear logs when dialog opens to ensure we start with a clean slate
    const { clearLogs } = useExecutionStore.getState();
    clearLogs();

    const initialValues: Record<string, any> = {};
    const initialErrors: Record<string, string> = {};

    // Initialize with fresh values - don't use previously entered values
    const timestamp = new Date().toISOString().replace("T", " ").substring(0, 19);
    console.log(
      `[${timestamp}] [ExecutionDialog] ========== INITIALIZING EXECUTION DIALOG WITH FRESH VALUES ==========`,
    );
    console.log(`[${timestamp}] [ExecutionDialog] Missing fields count: ${missingFields.length}`);
    // Note: We're intentionally not using window.startNodeCollectedParameters to ensure fresh input values

    // Log details about missing fields
    if (missingFields.length > 0) {
      console.log(`[${timestamp}] [ExecutionDialog] Missing fields details:`);
      missingFields.forEach((field, index) => {
        console.log(
          `[${timestamp}] [ExecutionDialog]   ${index + 1}. Node: ${field.nodeName} (${field.nodeId}), Field: ${field.displayName} (${field.name}), Type: ${field.inputType}`,
        );
        console.log("The field", field);
        // Ensure field has proper display_name and node_name
        if (!field.displayName) {
          console.log(
            `[${timestamp}] [ExecutionDialog] WARNING: Field ${index + 1} is missing display_name, using name instead`,
          );
          field.displayName = field.name || "Unnamed Field";
        }

        if (!field.nodeId) {
          console.log(
            `[${timestamp}] [ExecutionDialog] WARNING: Field ${index + 1} is missing node_name, using "Unknown Node" instead`,
          );
          field.nodeName = "Unknown Node";
        }
      });
    }

    // Get the current nodes from the window object
    // This is used to check if a node still exists in the workflow
    const currentNodeIds = new Set(window.currentWorkflowNodes?.map((node: any) => node.id) || []);
    console.log(
      `[${timestamp}] [ExecutionDialog] Current node IDs in workflow:`,
      Array.from(currentNodeIds),
    );

    // For prebuilt workflows, the currentNodeIds might be empty even though the nodes exist
    // In this case, we should trust the missingFields array which was populated by the validation
    const isPrebuiltWorkflow = currentNodeIds.size === 0 && missingFields.length > 0;
    if (isPrebuiltWorkflow) {
      console.log(
        `[${timestamp}] [ExecutionDialog] Detected prebuilt workflow - currentNodeIds is empty but missingFields has ${missingFields.length} fields`,
      );
      console.log(
        `[${timestamp}] [ExecutionDialog] Will not filter out fields based on node existence for prebuilt workflow`,
      );
    }

    // Create a list of all fields that should be displayed
    // This includes both missing fields and fields with values in the StartNode
    // Filter out fields from nodes that no longer exist in the workflow, but only if not a prebuilt workflow
    // Also filter out fields that are not connected to the Start node
    // And filter out fields that already have values configured in the inspector panel
    // CRITICAL FIX: Also filter out fields from tool-connected components (defensive check)
    const allFields = [...missingFields].filter((field) => {
      // DEFENSIVE CHECK: Skip fields from nodes connected as tools to agentic components
      // This is a safety net in case tool parameters slip through the validation system
      const currentEdges = window.currentWorkflowEdges || [];
      if (isNodeConnectedAsTool(field.nodeId, currentEdges)) {
        console.log(
          `[${timestamp}] [ExecutionDialog] DEFENSIVE FILTER: Excluding field from tool-connected node: ${field.nodeName} (${field.nodeId}) - field: ${field.name}`,
        );
        return false;
      }
      // For prebuilt workflows, skip the node existence check
      if (isPrebuiltWorkflow) {
        // For loaded workflows, we need to handle the case where required might not be explicitly set
        // Consider fields required unless explicitly marked as optional (required: false)
        const isRequired = field.required !== false;
        // Also include fields directly connected to the Start node
        const isDirectlyConnected = field.directly_connected_to_start === true;

        // Check if the field already has a value configured in the inspector panel
        // First, find the node in the current workflow
        const node = window.currentWorkflowNodes?.find((node: any) => node.id === field.nodeId);

        // Check if the node has a config with a value for this field
        const hasConfiguredValue = node?.data?.config && node.data.config[field.name] !== undefined;

        // Check if this field has an incoming connection from another node
        const hasIncomingConnection = field.is_handle === true && field.is_connected === true;

        // Use the shared utility function to log field status
        logFieldStatus(
          "ExecutionDialog",
          `${field.nodeId}_${field.name}`,
          field.nodeName,
          field.name,
          isRequired,
          field.required,
          isDirectlyConnected,
          hasConfiguredValue,
          hasConfiguredValue ? node.data.config[field.name] : undefined,
          hasIncomingConnection,
        );

        // Use the shared utility function to determine if the field should be included
        return shouldIncludeField(
          isRequired,
          isDirectlyConnected,
          hasConfiguredValue,
          hasIncomingConnection,
        );
      }

      // For regular workflows, first check if the node still exists
      const nodeExists = currentNodeIds.has(field.nodeId);
      if (!nodeExists) {
        console.log(
          `[${timestamp}] [ExecutionDialog] Filtering out field from deleted node: ${field.nodeName} (${field.nodeId})`,
        );
        return false;
      }

      // Check if the node is connected to the Start node
      if (!field.connected_to_start) {
        console.log(
          `[${timestamp}] [ExecutionDialog] Filtering out field from node not connected to Start node: ${field.nodeName} (${field.nodeId})`,
        );
        return false;
      }

      // For loaded workflows, we need to handle the case where required might not be explicitly set
      // Consider fields required unless explicitly marked as optional (required: false)
      const isRequired = field.required !== false;
      // Also include fields directly connected to the Start node
      const isDirectlyConnected = field.directly_connected_to_start === true;

      // Check if the field already has a value configured in the inspector panel
      // Find the node in the current workflow
      const node = window.currentWorkflowNodes?.find((node: any) => node.id === field.nodeId);

      // Check if the node has a config with a value for this field
      const hasConfiguredValue = node?.data?.config && node.data.config[field.name] !== undefined;

      // Check if this field has an incoming connection from another node
      const hasIncomingConnection = field.is_handle === true && field.is_connected === true;

      // Use the shared utility function to log field status
      logFieldStatus(
        "ExecutionDialog",
        `${field.nodeId}_${field.name}`,
        field.nodeName,
        field.name,
        isRequired,
        field.required,
        isDirectlyConnected,
        hasConfiguredValue,
        hasConfiguredValue ? node.data.config[field.name] : undefined,
        hasIncomingConnection,
      );

      // Use the shared utility function to determine if the field should be included
      return shouldIncludeField(
        isRequired,
        isDirectlyConnected,
        hasConfiguredValue,
        hasIncomingConnection,
      );
    });

    // We're not using StartNode parameters anymore to ensure fresh input values
    // Instead, we'll just use the missing fields that were collected during validation

    // Log the fields we're going to use
    console.log(
      `[${timestamp}] [ExecutionDialog] Using ${allFields.length} fields from validation`,
    );

    // No need to update missing fields since we're not adding any new fields from StartNode parameters

    // Get a snapshot of the current fieldValues to avoid dependency issues
    const currentFieldValues = { ...fieldValues };

    // Process all fields to set initial values
    console.log(
      `[${timestamp}] [ExecutionDialog] ========== PROCESSING FIELDS FOR INITIALIZATION ==========`,
    );
    console.log(`[${timestamp}] [ExecutionDialog] Total fields to process: ${allFields.length}`);
    console.log(
      `[${timestamp}] [ExecutionDialog] Existing field values: ${Object.keys(currentFieldValues).length}`,
    );

    allFields.forEach((field) => {
      const fieldId = `${field.nodeId}_${field.name}`;
      console.log(`[${timestamp}] [ExecutionDialog] Processing field: ${fieldId}`);
      console.log(
        `[${timestamp}] [ExecutionDialog] Field details - Node: ${field.nodeName}, Name: ${field.name}, Type: ${field.inputType}`,
      );

      // Always use fresh values, even if we already have a value for this field in the current session
      if (currentFieldValues[fieldId] !== undefined) {
        console.log(
          `[${timestamp}] [ExecutionDialog] Not using existing value for ${fieldId} to ensure fresh input`,
        );
      }

      // Check if the field has a currentValue from the missing fields collection
      if (field.currentValue !== undefined) {
        console.log(
          `[${timestamp}] [ExecutionDialog] Raw currentValue from field collection for ${fieldId}:`,
          field.currentValue,
        );
        console.log(
          `[${timestamp}] [ExecutionDialog] currentValue type: ${typeof field.currentValue}`,
        );

        // Always use currentValue as-is since it should already be properly unwrapped
        // The unwrapping should have happened in fieldValidation.ts
        console.log(
          `[${timestamp}] [ExecutionDialog] Using currentValue as-is for ${fieldId}:`,
          field.currentValue,
        );
        initialValues[fieldId] = field.currentValue;
      } else {
        console.log(
          `[${timestamp}] [ExecutionDialog] No currentValue found, using default values for field ${fieldId}`,
        );

        // If no value in StartNode, use default values based on input type
        console.log(
          `[${timestamp}] [ExecutionDialog] Setting default value for field ${field.name} (${field.inputType})`,
        );

        // Handle different input types with appropriate defaults
        switch (field.inputType) {
          case "string":
          case "text":
            initialValues[fieldId] = "";
            console.log(
              `[${timestamp}] [ExecutionDialog] Set default empty string for string field ${fieldId}`,
            );
            break;

          case "number":
          case "int":
          case "float":
            initialValues[fieldId] = 0;
            console.log(
              `[${timestamp}] [ExecutionDialog] Set default 0 for numeric field ${fieldId}`,
            );
            break;

          case "boolean":
          case "bool":
            initialValues[fieldId] = false;
            console.log(
              `[${timestamp}] [ExecutionDialog] Set default false for boolean field ${fieldId}`,
            );
            break;

          case "dropdown":
            // For dropdowns, use the first option or empty string
            if (field.options && field.options.length > 0) {
              initialValues[fieldId] = field.options[0];
              console.log(
                `[${timestamp}] [ExecutionDialog] Set default first option "${field.options[0]}" for dropdown field ${fieldId}`,
              );
            } else {
              initialValues[fieldId] = "";
              console.log(
                `[${timestamp}] [ExecutionDialog] Set default empty string for dropdown field ${fieldId} (no options available)`,
              );
            }
            break;

          case "object":
          case "dict":
          case "json":
            // Try to extract properties from the field info
            try {
              // Special case for keywords field
              if (field.name === "keywords" || field.name === "tool_arg_keywords") {
                const keywordsObj = {
                  time: "",
                  objective: "",
                  audience: "",
                  gender: "",
                  tone: "",
                  speakers: "",
                };
                initialValues[fieldId] = keywordsObj; // Store as native object, not JSON string
                console.log(
                  `[${timestamp}] [ExecutionDialog] Set default keywords object for field ${fieldId}:`,
                  keywordsObj,
                );
              } else {
                // For other object fields, use empty object as native object
                initialValues[fieldId] = {}; // Store as native object, not JSON string
                console.log(
                  `[${timestamp}] [ExecutionDialog] Set default empty object for object field ${fieldId}`,
                );
              }
            } catch (e) {
              console.error(
                `[${timestamp}] [ExecutionDialog] Error initializing object field ${field.name}:`,
                e,
              );
              initialValues[fieldId] = {}; // Store as native object, not JSON string
              console.log(
                `[${timestamp}] [ExecutionDialog] Set default empty object after error for field ${fieldId}`,
              );
            }
            break;

          case "array":
          case "list":
            initialValues[fieldId] = []; // Store as native array, not JSON string
            console.log(
              `[${timestamp}] [ExecutionDialog] Set default empty array for array field ${fieldId}`,
            );
            break;

          case "credential":
            initialValues[fieldId] = "";
            console.log(
              `[${timestamp}] [ExecutionDialog] Set default empty string for credential field ${fieldId}`,
            );
            break;

          default:
            initialValues[fieldId] = "";
            console.log(
              `[${timestamp}] [ExecutionDialog] Set default empty string for unknown type field ${fieldId} (type: ${field.inputType})`,
            );
            break;
        }
      }

      console.log(
        `[${timestamp}] [ExecutionDialog] Final value for ${fieldId}: ${JSON.stringify(initialValues[fieldId])}`,
      );

      // Log whether this field will have an initial error
      if (field.inputType !== "boolean" && !initialValues[fieldId]) {
        console.log(
          `[${timestamp}] [ExecutionDialog] Field ${fieldId} will have initial error: This field is required`,
        );
      }

      // Set initial error if field is empty
      if (field.inputType !== "boolean" && !initialValues[fieldId]) {
        initialErrors[fieldId] = "This field is required";
      }
    });

    // Only update field values if we have new values to set
    if (Object.keys(initialValues).length > 0) {
      console.log(`[${timestamp}] [ExecutionDialog] ========== UPDATING FORM STATE ==========`);
      console.log(
        `[${timestamp}] [ExecutionDialog] Setting ${Object.keys(initialValues).length} initial field values`,
      );

      // Log each field being set
      Object.entries(initialValues).forEach(([fieldId, value]) => {
        console.log(
          `[${timestamp}] [ExecutionDialog] Setting field ${fieldId} = ${JSON.stringify(value)}`,
        );
      });

      // Log initial errors
      if (Object.keys(initialErrors).length > 0) {
        console.log(
          `[${timestamp}] [ExecutionDialog] Setting ${Object.keys(initialErrors).length} initial field errors`,
        );
        Object.entries(initialErrors).forEach(([fieldId, errorMsg]) => {
          console.log(`[${timestamp}] [ExecutionDialog] Field error: ${fieldId} - ${errorMsg}`);
        });
      } else {
        console.log(`[${timestamp}] [ExecutionDialog] No initial field errors`);
      }

      // Merge with existing values
      const mergedValues = {
        ...currentFieldValues,
        ...initialValues,
      };

      // Get a snapshot of the current errors to avoid dependency issues
      const currentErrors = { ...errors };
      const mergedErrors = {
        ...currentErrors,
        ...initialErrors,
      };

      console.log(
        `[${timestamp}] [ExecutionDialog] Total field values after merge: ${Object.keys(mergedValues).length}`,
      );
      console.log(
        `[${timestamp}] [ExecutionDialog] Total field errors after merge: ${Object.keys(mergedErrors).length}`,
      );

      // Update state
      setFieldValues(mergedValues);
      setErrors(mergedErrors);

      // Validate form
      console.log(`[${timestamp}] [ExecutionDialog] Validating form with merged values and errors`);
      const isValid = validateForm(mergedValues, mergedErrors);

      // Force immediate update of button state if needed
      if (isValid) {
        console.log(
          `[${timestamp}] [ExecutionDialog] Initial form validation result: VALID, Run button should be enabled`,
        );
        // Dispatch a custom event for immediate UI updates
        window.dispatchEvent(new CustomEvent("form-validation-complete", { detail: { isValid } }));
      } else {
        console.log(
          `[${timestamp}] [ExecutionDialog] Initial form validation result: INVALID, Run button should be disabled`,
        );
      }
    } else {
      console.log(`[${timestamp}] [ExecutionDialog] No new field values to set`);
    }

    // No need for cleanup function here since we handle the reset in the main effect body
  }, [isDialogOpen, missingFields, validateForm]);

  // Auto-scroll logs to bottom
  useEffect(() => {
    if (logsEndRef.current) {
      logsEndRef.current.scrollIntoView({ behavior: "smooth" });
    }
  }, [logs]);

  // Clean up SSE connection when component unmounts
  useEffect(() => {
    return () => {
      // Close any existing SSE connection
      if (sseClientRef.current) {
        console.log("Closing SSE connection on component unmount");
        sseClientRef.current.close();
        sseClientRef.current = null;
      }

      // Reset workflow status
      setWorkflowStatus(null);
    };
  }, []);

  // Clear logs when dialog closes
  useEffect(() => {
    if (!isDialogOpen) {
      // Clear logs when dialog closes
      const { clearLogs } = useExecutionStore.getState();
      clearLogs();
    }
  }, [isDialogOpen]);

  // State for approval request and workflow status
  const [approvalNeeded, setApprovalNeeded] = useState(false);
  const [workflowStatus, setWorkflowStatus] = useState<string | null>(null);
  const [approvalDetails, setApprovalDetails] = useState<{
    correlationId: string;
    nodeId: string;
    nodeName: string;
    timestamp?: number;
    approvalKey?: string;
  } | null>(null);

  // State for dialog content warnings (currently unused but kept for future use)
  const [, setDialogContentWarnings] = useState<string[]>([]);

  // Function to add default descriptions to dialog content
  const addDefaultDescriptions = useCallback(() => {
    const timestamp = new Date().toISOString().replace("T", " ").substring(0, 19);
    console.log(
      `[${timestamp}] [addDefaultDescriptions] Adding default descriptions to dialog content`,
    );

    // This is a temporary fix - in the future, these descriptions should be added
    // in the node configuration where the dialog content is defined

    // Clear any existing warnings
    setDialogContentWarnings([]);

    // In a real implementation, you would modify the node configuration
    // For now, we'll just log that this action would be taken
    console.log(
      `[${timestamp}] [addDefaultDescriptions] In a real implementation, this would modify the node configuration to add descriptions`,
    );

    // Add a log entry about fixing the warnings
    addLog("✅ Added default descriptions to dialog content");
  }, [addLog]);

  // Listen for workflow terminal status events
  useEffect(() => {
    const handleTerminalStatus = (event: CustomEvent) => {
      console.log("Received workflow-terminal-status event:", event.detail);

      // Check if we're in approval state - don't reset approval-related states if we are
      const isInApprovalState = workflowStatus === "waiting_for_approval" && approvalNeeded;

      // Reset execution state
      setIsExecuting(false);
      setIsStreaming(false);

      // If we're not in approval state, reset form validation and field values
      if (!isInApprovalState) {
        // Reset form validation state
        // We'll set this to false so that the user has to fill in the form again
        setIsFormValid(false);

        // Reset field values to empty to force user to fill them in again
        const emptyValues: Record<string, any> = {};
        const newErrors: Record<string, string> = {};

        // Initialize empty values for all fields
        missingFields.forEach((field) => {
          const fieldId = `${field.nodeId}_${field.name}`;

          // Set empty values based on field type
          if (field.inputType === "boolean" || field.inputType === "bool") {
            emptyValues[fieldId] = false;
          } else if (
            field.inputType === "number" ||
            field.inputType === "int" ||
            field.inputType === "float"
          ) {
            emptyValues[fieldId] = 0;
          } else if (
            field.inputType === "object" ||
            field.inputType === "dict" ||
            field.inputType === "json"
          ) {
            emptyValues[fieldId] = "{}";
          } else if (field.inputType === "array" || field.inputType === "list") {
            emptyValues[fieldId] = "[]";
          } else {
            emptyValues[fieldId] = "";
          }

          // Set validation errors for required fields
          if (
            field.required !== false &&
            field.inputType !== "boolean" &&
            field.inputType !== "bool"
          ) {
            newErrors[fieldId] = "This field is required";
          }
        });

        // Update field values and errors
        setFieldValues(emptyValues);
        setErrors(newErrors);

        // Only clear approval state if we're not in approval state
        setApprovalNeeded(false);
        setWorkflowStatus(null);

        // Clear from the tracking system if we have details
        if (approvalDetails) {
          clearApprovalEvent(approvalDetails.correlationId, approvalDetails.nodeId);
        }

        setApprovalDetails(null);
      } else {
        // If we are in approval state, make sure the Run Again button is disabled
        setIsFormValid(false);
        console.log("Workflow is in approval state, keeping approval UI visible");
      }
    };

    // Add event listener
    window.addEventListener("workflow-terminal-status", handleTerminalStatus as EventListener);

    // Clean up
    return () => {
      window.removeEventListener("workflow-terminal-status", handleTerminalStatus as EventListener);
    };
  }, [
    setIsFormValid,
    setIsStreaming,
    setIsExecuting,
    setFieldValues,
    setErrors,
    setApprovalNeeded,
    setWorkflowStatus,
    setApprovalDetails,
    missingFields,
    approvalDetails,
    clearApprovalEvent,
  ]);

  // Listen for form validation complete events
  useEffect(() => {
    const handleFormValidationComplete = (event: CustomEvent) => {
      const timestamp = new Date().toISOString().replace("T", " ").substring(0, 19);
      console.log(
        `[${timestamp}] [FormValidationComplete] Received form-validation-complete event:`,
        event.detail,
      );

      if (event.detail && event.detail.isValid) {
        console.log(
          `[${timestamp}] [FormValidationComplete] Form is valid, forcing button state update`,
        );
        // Force update the UI to enable the Run button
        setIsFormValid(true);
      }
    };

    // Add event listener
    window.addEventListener(
      "form-validation-complete",
      handleFormValidationComplete as EventListener,
    );

    // Clean up
    return () => {
      window.removeEventListener(
        "form-validation-complete",
        handleFormValidationComplete as EventListener,
      );
    };
  }, [setIsFormValid]);

  // Listen for workflow approval needed events
  useEffect(() => {
    let lastProcessedTimestamp = 0;

    const handleApprovalNeeded = (event: CustomEvent) => {
      console.log("Received workflow-approval-needed event:", event.detail);

      // Force processing of events that have the approvalKey property (from our improved utility)
      // or if it's a newer event than the last one we processed
      // or if the event has the force flag set
      const shouldProcess =
        event.detail.approvalKey ||
        event.detail.force ||
        !event.detail.timestamp ||
        event.detail.timestamp > lastProcessedTimestamp;

      if (shouldProcess) {
        lastProcessedTimestamp = event.detail.timestamp || Date.now();

        // Switch to logs tab to show the approval UI
        setActiveTab("logs");

        // Set approval state with a slight delay to ensure UI updates
        // This helps with race conditions in React's state updates
        setTimeout(() => {
          setApprovalNeeded(true);
          setWorkflowStatus("waiting_for_approval");
          setApprovalDetails(event.detail);

          // Log the approval state for debugging
          console.log("Setting approval state:", {
            approvalNeeded: true,
            workflowStatus: "waiting_for_approval",
            approvalDetails: event.detail,
          });
        }, 100);

        console.log(
          `Processed approval event for node ${event.detail.nodeName} (${event.detail.nodeId}) with timestamp ${lastProcessedTimestamp}`,
        );
      } else {
        console.log(
          `Ignoring older approval event with timestamp ${event.detail.timestamp} (last processed: ${lastProcessedTimestamp})`,
        );
      }
    };

    // Handle direct UI update events
    const handleApprovalUIUpdate = () => {
      console.log("Received approval-ui-update event");

      // Check if we have a pending approval that needs to be displayed
      if (window._pendingApproval) {
        console.log("Processing pending approval from UI update event:", window._pendingApproval);

        // Switch to logs tab
        setActiveTab("logs");

        // Set approval state
        setApprovalNeeded(true);
        setApprovalDetails({
          correlationId: window._pendingApproval.correlationId,
          nodeId: window._pendingApproval.nodeId,
          nodeName: window._pendingApproval.nodeName,
          timestamp: window._pendingApproval.timestamp,
        });
      }
    };

    // Add event listeners
    window.addEventListener("workflow-approval-needed", handleApprovalNeeded as EventListener);
    window.addEventListener("approval-ui-update", handleApprovalUIUpdate as EventListener);

    // Add a periodic check for pending approvals that might have been missed
    const checkInterval = setInterval(() => {
      if (window._pendingApproval && !approvalNeeded) {
        console.log("Found pending approval that wasn't processed:", window._pendingApproval);

        // Manually trigger the approval UI
        setApprovalNeeded(true);
        setApprovalDetails({
          correlationId: window._pendingApproval.correlationId,
          nodeId: window._pendingApproval.nodeId,
          nodeName: window._pendingApproval.nodeName,
          timestamp: window._pendingApproval.timestamp,
        });

        // Switch to logs tab
        setActiveTab("logs");
      }
    }, 2000);

    // Clean up
    return () => {
      window.removeEventListener("workflow-approval-needed", handleApprovalNeeded as EventListener);
      window.removeEventListener("approval-ui-update", handleApprovalUIUpdate as EventListener);
      clearInterval(checkInterval);
      // Clear all approval events when component unmounts
      clearAllApprovalEvents();
    };
  }, [setActiveTab, approvalNeeded]);

  // Check logs for approval needed status
  useEffect(() => {
    // Only check if we're not already in approval mode and we have logs
    if (!approvalNeeded && logs.length > 0 && correlationId) {
      // Check the last 10 logs (or all logs if fewer than 10)
      const logsToCheck = logs.slice(Math.max(0, logs.length - 10));

      for (const log of logsToCheck) {
        // Only look for logs that match our specific criteria
        if (
          log.includes("workflow_status") &&
          log.includes("waiting_for_approval") &&
          log.includes("approval_required") &&
          log.includes("status") &&
          log.includes("paused")
        ) {
          console.log("Found potential approval request in logs, checking details");

          try {
            const logData = JSON.parse(log);

            // Only consider it a valid approval request if it meets specific criteria
            if (
              logData.workflow_status === "waiting_for_approval" &&
              logData.approval_required === true &&
              logData.status === "paused" &&
              logData.node_id
            ) {
              console.log("Found valid approval request in logs:", {
                correlationId,
                nodeId: logData.node_id,
                nodeName: logData.node_name || logData.node_id,
              });

              // Set approval state
              setApprovalNeeded(true);
              setApprovalDetails({
                correlationId,
                nodeId: logData.node_id,
                nodeName: logData.node_name || logData.node_id,
                timestamp: Date.now(),
              });

              // Switch to logs tab
              setActiveTab("logs");

              // Also set the window flag directly for immediate access
              window._pendingApproval = {
                correlationId,
                nodeId: logData.node_id,
                nodeName: logData.node_name || logData.node_id,
                timestamp: Date.now(),
              };

              // Add to approval history for debugging
              if (window._approvalEventHistory) {
                window._approvalEventHistory.push({
                  correlationId,
                  nodeId: logData.node_id,
                  nodeName: logData.node_name || logData.node_id,
                  timestamp: Date.now(),
                  status: "detected_in_logs",
                });
              }

              // Force a UI update
              setTimeout(() => {
                window.dispatchEvent(new CustomEvent("approval-ui-update"));
              }, 200);

              break;
            } else {
              console.log("Log contains waiting_for_approval but doesn't meet criteria:", logData);
            }
          } catch (e) {
            console.error("Error parsing log data:", e);
          }
        }
      }
    }
  }, [logs, approvalNeeded, correlationId, setActiveTab]);

  // Handle when approval is sent
  const handleApprovalSent = () => {
    console.log("Approval sent, waiting for workflow to continue...");

    // Add to approval history for debugging
    if (window._approvalEventHistory && approvalDetails) {
      window._approvalEventHistory.push({
        correlationId: approvalDetails.correlationId,
        nodeId: approvalDetails.nodeId,
        nodeName: approvalDetails.nodeName || approvalDetails.nodeId,
        timestamp: Date.now(),
        status: "approval_sent",
      });
    }

    // Clear approval state completely
    setApprovalNeeded(false);

    // Reset workflow status to running
    setWorkflowStatus("running");

    // Clear from the tracking system if we have details
    if (approvalDetails) {
      clearApprovalEvent(approvalDetails.correlationId, approvalDetails.nodeId);

      // Also clear the window flag
      window._pendingApproval = undefined;
    }

    // Reset approval details
    setApprovalDetails(null);

    // Log the state change
    console.log("Approval state cleared after sending approval");
  };

  // Handle field change - memoized to prevent unnecessary re-renders
  const handleFieldChange = useCallback(
    (fieldId: string, value: any, type: string) => {
      const timestamp = new Date().toISOString().replace("T", " ").substring(0, 19);
      console.log(`[${timestamp}] [handleFieldChange] ========== FIELD CHANGE DETECTED ==========`);
      console.log(`[${timestamp}] [handleFieldChange] Field: ${fieldId}`);
      console.log(`[${timestamp}] [handleFieldChange] Type: ${type}`);
      console.log(`[${timestamp}] [handleFieldChange] New value: ${JSON.stringify(value)}`);
      console.log(
        `[${timestamp}] [handleFieldChange] Previous value: ${JSON.stringify(fieldValues[fieldId])}`,
      );

      // Extract node ID and field name from the field ID for logging purposes
      const parts = fieldId.split("_");
      const nodeId = parts[0];
      const fieldName = parts.slice(1).join("_");
      console.log(
        `[${timestamp}] [handleFieldChange] Field belongs to node ${nodeId}, field name: ${fieldName}`,
      );

      // Find the field definition to check if it's required
      const fieldDef = missingFields.find((field) => `${field.nodeId}_${field.name}` === fieldId);
      // Consider fields required unless explicitly marked as optional
      const isRequired = fieldDef?.required !== false;
      console.log(
        `[${timestamp}] [handleFieldChange] Field is required: ${isRequired ? "YES" : "NO"} (required !== false: ${fieldDef?.required !== false})`,
      );

      // Validate field
      const newErrors = { ...errors };
      console.log(
        `[${timestamp}] [handleFieldChange] Current errors: ${JSON.stringify(newErrors[fieldId] || "none")}`,
      );

      // Perform validation based on field type
      if (isRequired && type !== "boolean" && !value) {
        console.log(
          `[${timestamp}] [handleFieldChange] VALIDATION ERROR: Field is required but empty`,
        );
        newErrors[fieldId] = "This field is required";
      } else if (
        type === "object" ||
        type === "dict" ||
        type === "json" ||
        type === "array" ||
        type === "list"
      ) {
        console.log(`[${timestamp}] [handleFieldChange] Validating ${type} field`);

        if (typeof value === "string" && value.trim() !== "") {
          // Validate JSON string format
          try {
            JSON.parse(value);
            console.log(`[${timestamp}] [handleFieldChange] JSON string validation passed`);
            delete newErrors[fieldId];
          } catch (e: any) {
            console.log(
              `[${timestamp}] [handleFieldChange] VALIDATION ERROR: Invalid JSON format - ${e.message}`,
            );
            newErrors[fieldId] = "Invalid JSON format";
          }
        } else if (typeof value === "object" && value !== null) {
          // Native object - validate it's a valid object structure
          try {
            // Test if the object can be serialized (catches circular references, etc.)
            JSON.stringify(value);
            console.log(`[${timestamp}] [handleFieldChange] Native object validation passed`);
            delete newErrors[fieldId];
          } catch (e: any) {
            console.log(
              `[${timestamp}] [handleFieldChange] VALIDATION ERROR: Invalid object structure - ${e.message}`,
            );
            newErrors[fieldId] = "Invalid object structure";
          }
        } else if (
          value === "" ||
          value === null ||
          value === undefined ||
          (Array.isArray(value) && value.length === 0) ||
          (typeof value === "object" && Object.keys(value).length === 0)
        ) {
          // Empty value is valid (will be handled by required field validation)
          console.log(`[${timestamp}] [handleFieldChange] Empty ${type} field is valid`);
          delete newErrors[fieldId];
        } else {
          console.log(
            `[${timestamp}] [handleFieldChange] VALIDATION ERROR: Invalid ${type} value type`,
          );
          newErrors[fieldId] = `Invalid ${type} value`;
        }
      } else {
        console.log(`[${timestamp}] [handleFieldChange] Field validation passed`);
        delete newErrors[fieldId];
      }

      // Update field value in store
      console.log(`[${timestamp}] [handleFieldChange] Updating field value in store`);
      updateFieldValue(fieldId, value);

      // Log validation result
      if (newErrors[fieldId]) {
        console.log(`[${timestamp}] [handleFieldChange] Field has error: ${newErrors[fieldId]}`);
      } else {
        console.log(`[${timestamp}] [handleFieldChange] Field is valid`);
      }

      // Update errors
      console.log(`[${timestamp}] [handleFieldChange] Updating errors in state`);
      setErrors(newErrors);

      // Create merged values for form validation
      const mergedValues = {
        ...fieldValues,
        [fieldId]: value,
      };

      console.log(`[${timestamp}] [handleFieldChange] Validating entire form with updated values`);

      // Validate form - use the return value to update local state immediately
      const isValid = validateForm(mergedValues, newErrors);
      console.log(
        `[${timestamp}] [handleFieldChange] Form validation result: ${isValid ? "VALID" : "INVALID"}`,
      );

      // Force immediate update of button state if needed
      if (isValid) {
        console.log(
          `[${timestamp}] [handleFieldChange] Form is now valid, Run button should be enabled`,
        );
        // We could dispatch a custom event here if needed for immediate UI updates
        window.dispatchEvent(new CustomEvent("form-validation-complete", { detail: { isValid } }));
      }
    },
    [missingFields, errors, fieldValues, updateFieldValue, setErrors, validateForm],
  );

  // Function to set up SSE connection
  const setupSSEConnection = useCallback(
    (correlationId: string) => {
      const timestamp = new Date().toISOString().replace("T", " ").substring(0, 19);
      console.log(
        `[${timestamp}] [setupSSEConnection] Setting up SSE connection for correlation ID: ${correlationId}`,
      );

      // Close any existing connection
      if (sseClientRef.current) {
        console.log(`[${timestamp}] [setupSSEConnection] Closing existing SSE connection`);
        sseClientRef.current.close();
        sseClientRef.current = null;
      }

      // Create a new SSE client
      const sseClient = new SSEClient(correlationId, {
        onOpen: () => {
          console.log(`[${timestamp}] [setupSSEConnection] SSE connection opened`);
          setIsStreaming(true);
          setWorkflowStatus("running");
          addLog("Connected to execution stream...");
        },
        onMessage: (event) => {
          // This handles any unnamed events from the server
          const timestamp = new Date().toISOString().replace("T", " ").substring(0, 19);
          console.log(`[${timestamp}] [setupSSEConnection] SSE generic message received:`, event.data);
          // Avoid logging empty keep-alive messages
          if (event.data && event.data !== "{}") {
            addLog(event.data);
          }
        },
        onCustomEvent: (eventType, data) => {
          const timestamp = new Date().toISOString().replace("T", " ").substring(0, 19);
          console.log(`[${timestamp}] [setupSSEConnection] SSE custom event received [${eventType}]:`, data);

          const logData = typeof data === "object" && data !== null ? JSON.stringify(data, null, 2) : data;

          switch (eventType) {
            case "connection":
              addLog(`Stream connected: ${logData}`);
              break;
            case "keep-alive":
              // Don't log keep-alive messages to avoid clutter
              break;
            case "workflow-update":
              addLog(logData);
              if (data && data.workflow_status) {
                const status = data.workflow_status.toLowerCase();
                setWorkflowStatus(status);
                if (
                  status === "waiting_for_approval" &&
                  data.approval_required === true &&
                  data.status === "paused"
                ) {
                  const nodeName = data.node_name || data.node_id || "Unknown";
                  addLog(`⏸️ Workflow is waiting for approval. Node: ${nodeName}`);
                }
              }
              break;
            case "workflow-completed":
              addLog(`✅ Workflow completed: ${logData}`);
              setWorkflowStatus("completed");
              setIsExecuting(false);
              setIsStreaming(false);
              break;
            case "workflow-failed":
              addLog(`❌ Workflow failed: ${logData}`);
              setWorkflowStatus("failed");
              setIsExecuting(false);
              setIsStreaming(false);
              break;
            case "workflow-cancelled":
              addLog(`⚠️ Workflow cancelled: ${logData}`);
              setWorkflowStatus("cancelled");
              setIsExecuting(false);
              setIsStreaming(false);
              break;
            case "error":
              addLog(`❌ Error: ${logData}`);
              break;
            case "update":
            case "message":
            default:
              if (logData && logData !== "{}") {
                addLog(logData);
              }
              break;
          }
        },
        onError: (error) => {
          console.error(`[${timestamp}] [setupSSEConnection] SSE connection error:`, error);
          setIsStreaming(false);
          addLog("❌ Error in execution stream connection");
        },
        onClose: (wasError) => {
          console.log(
            `[${timestamp}] [setupSSEConnection] SSE connection closed`,
            wasError ? "due to an error" : "",
          );
          setIsStreaming(false);
        },
      });

      // Store the client in the ref
      sseClientRef.current = sseClient;

      // Connect to the SSE stream
      sseClient.connect();

      console.log(`[${timestamp}] [setupSSEConnection] SSE connection setup complete`);
    },
    [addLog, setIsStreaming, setIsExecuting, setWorkflowStatus],
  );

  // Handle form submission - memoized to prevent unnecessary re-renders
  const handleSubmit = useCallback(async () => {
    const timestamp = new Date().toISOString().replace("T", " ").substring(0, 19);
    console.log(`[${timestamp}] [handleSubmit] ========== FORM SUBMISSION INITIATED ==========`);
    console.log(`[${timestamp}] [handleSubmit] Form is valid: ${isFormValid ? "YES" : "NO"}`);

    if (!isFormValid) {
      console.log(`[${timestamp}] [handleSubmit] Submission blocked - form is not valid`);
      return;
    }

    // Clear previous logs before starting a new execution
    const { clearLogs } = useExecutionStore.getState();
    clearLogs();

    // Set workflow status to running
    setWorkflowStatus("running");

    try {
      console.log(`[${timestamp}] [handleSubmit] Executing workflow with values from the store...`);
      console.log(
        `[${timestamp}] [handleSubmit] Total field values: ${Object.keys(fieldValues).length}`,
      );

      // Log all field values being submitted
      Object.entries(fieldValues).forEach(([fieldId, value]) => {
        console.log(
          `[${timestamp}] [handleSubmit] Field value: ${fieldId} = ${JSON.stringify(value)}`,
        );
      });

      // Switch to logs tab before execution
      console.log(`[${timestamp}] [handleSubmit] Switching to logs tab`);
      setActiveTab("logs");

      // Use workflow_id from props or fallback to URL params for backward compatibility
      const workflow_id =
        workflowId ||
        (() => {
          const urlParams = new URLSearchParams(window.location.search);
          return urlParams.get("workflow_id");
        })();

      if (!workflow_id) {
        console.error(
          `[${timestamp}] [handleSubmit] No workflow_id found in props or URL parameters`,
        );
        addLog("❌ Error: No workflow ID found. Cannot execute workflow.");
        return;
      }

      console.log(`[${timestamp}] [handleSubmit] Using workflow_id: ${workflow_id}`);
      addLog(`Preparing to execute workflow with ID: ${workflow_id}`);

      // Prepare the user_dependent_fields array and user_payload_template object
      const user_dependent_fields: string[] = [];
      const user_payload_template: Record<string, any> = {};

      // Process field values for the execution payload
      missingFields.forEach((field) => {
        const fieldId = `${field.nodeId}_${field.name}`;
        let value = fieldValues[fieldId];

        if (value !== undefined) {
          // Add field name to dependent fields array
          user_dependent_fields.push(field.name);

          console.log(
            `[${timestamp}] [handleSubmit] Processing field ${field.name}, raw value:`,
            value,
          );
          console.log(
            `[${timestamp}] [handleSubmit] Value type: ${typeof value}, is object: ${typeof value === "object"}`,
          );

          // Check if the value is still wrapped and unwrap it here as a fallback
          if (typeof value === "object" && value !== null && "value" in value) {
            console.log(
              `[${timestamp}] [handleSubmit] DETECTED WRAPPED VALUE - unwrapping:`,
              value,
            );
            value = value.value;
            console.log(`[${timestamp}] [handleSubmit] Unwrapped value:`, value);
          }

          // Determine the processed value based on field type
          let processedValue;
          // Pass the value as-is without any parsing or transformation
          console.log("the processed value is printed here", value);
          processedValue = value;
          console.log(
            `[${timestamp}] [handleSubmit] Using value as-is for field ${field.name}:`,
            processedValue,
          );

          // Create the field entry with proper structure
          // Keep transition_id for workflow routing, but don't wrap the value in another {value: ...} object
          user_payload_template[field.name] = {
            value: processedValue,
            transition_id: field.nodeId,
          };

          console.log(
            `[${timestamp}] [handleSubmit] Final processed field ${field.name}:`,
            user_payload_template[field.name],
          );
        }
      });

      console.log(
        `[${timestamp}] [handleSubmit] Prepared user_dependent_fields:`,
        user_dependent_fields,
      );
      console.log(
        `[${timestamp}] [handleSubmit] Prepared user_payload_template:`,
        user_payload_template,
      );

      // Log the structure of the payload template for debugging
      console.log(
        `[${timestamp}] [handleSubmit] Payload template structure analysis:`,
        Object.entries(user_payload_template).map(([key, value]) => ({
          field: key,
          hasTransitionId: typeof value === "object" && value !== null && "transition_id" in value,
          hasValue: typeof value === "object" && value !== null && "value" in value,
          type: typeof value,
          structure: typeof value === "object" && value !== null ? Object.keys(value) : "primitive",
        })),
      );

      // Create the execution payload
      const executionPayload: WorkflowExecuteWithUserInputsPayload = {
        workflow_id: workflow_id,
        approval: true, // Always set to true (boolean) for user-initiated executions
        payload: {
          user_dependent_fields: user_dependent_fields,
          user_payload_template: user_payload_template,
        },
      };

      console.log(
        `[${timestamp}] [handleSubmit] Sending execution request with payload:`,
        executionPayload,
      );
      addLog("Sending workflow execution request...");

      // Set executing state
      setIsExecuting(true);

      // Execute the workflow with the new API
      const result = await executeWorkflowWithUserInputs(executionPayload);

      if (result.success) {
        console.log(`[${timestamp}] [handleSubmit] Execution request successful:`, result);
        addLog(`✅ Workflow execution started successfully`);

        // Check if we have a correlation ID for streaming
        if (result.correlationId) {
          setCorrelationId(result.correlationId);
          addLog(`Streaming logs with correlation ID: ${result.correlationId}`);

          // Set up SSE connection directly instead of calling onExecute
          console.log(`[${timestamp}] [handleSubmit] Setting up SSE connection directly`);
          setupSSEConnection(result.correlationId);
        } else {
          console.error(
            `[${timestamp}] [handleSubmit] No correlationId returned from execution API`,
          );
          addLog("⚠️ No correlation ID returned. Cannot stream execution logs.");
          setIsExecuting(false);
        }
      } else {
        console.error(`[${timestamp}] [handleSubmit] Execution request failed:`, result);
        addLog(`❌ Error executing workflow: ${result.message || "Unknown error"}`);
        setIsExecuting(false);
      }
    } catch (error) {
      console.error(`[${timestamp}] [handleSubmit] ERROR executing workflow:`, error);
      addLog(`❌ Error: ${error instanceof Error ? error.message : String(error)}`);
      setIsExecuting(false);
    }
  }, [
    isFormValid,
    fieldValues,
    missingFields,
    setActiveTab,
    addLog,
    setCorrelationId,
    setIsExecuting,
    setWorkflowStatus,
    setupSSEConnection,
  ]);

  // Download logs - memoized to prevent unnecessary re-renders
  const handleDownloadLogs = useCallback(() => {
    if (!Array.isArray(logs) || logs.length === 0) {
      console.warn("No logs available to download");
      return;
    }

    const blob = new Blob([logs.join("\n")], { type: "text/plain" });
    const url = URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.href = url;
    a.download = `workflow-execution-logs-${new Date().toISOString().replace(/:/g, "-")}.txt`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  }, [logs]);

  // Only render the Dialog when isDialogOpen is true to avoid unnecessary renders
  if (!isDialogOpen) {
    return null;
  }

  return (
    <Dialog
      open={true}
      onOpenChange={(open) => {
        if (!open) onClose();
      }}
    >
      <DialogContent className="flex max-h-[90vh] max-w-3xl flex-col">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            Workflow Execution
            {isStreaming && (
              <Badge
                variant="outline"
                className="ml-2 animate-pulse bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300"
              >
                Execution in progress
              </Badge>
            )}
          </DialogTitle>
        </DialogHeader>

        <div className="flex flex-grow flex-col overflow-hidden">
          <Tabs
            value={activeTab}
            onValueChange={setActiveTab}
            className="flex flex-grow flex-col overflow-hidden"
          >
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="parameters" className="flex items-center gap-2">
                <FileText className="h-4 w-4" />
                Required Parameters
              </TabsTrigger>
              <TabsTrigger
                value="logs"
                className="flex items-center gap-2"
                disabled={missingFields.length > 0 && !isFormValid}
              >
                <Terminal className="h-4 w-4" />
                Execution Logs
                {logs.length > 0 && (
                  <span className="bg-primary text-primary-foreground ml-1 flex h-5 w-5 items-center justify-center rounded-full text-xs">
                    {logs.length}
                  </span>
                )}
              </TabsTrigger>
            </TabsList>

            {/* Parameters Tab */}
            <TabsContent
              value="parameters"
              className="mt-4 flex flex-grow flex-col overflow-hidden"
            >
              {missingFields.length > 0 ? (
                <>
                  <div className="mb-4 flex items-start gap-2 rounded-md border border-blue-200 bg-blue-50 p-2">
                    <AlertCircle className="mt-0.5 h-5 w-5 flex-shrink-0 text-blue-500" />
                    <div>
                      <p className="text-sm font-medium text-blue-800">Workflow Parameters</p>
                      <p className="text-xs text-blue-700">
                        Please review and edit the following parameters before executing the
                        workflow. All values are editable.
                      </p>
                    </div>
                  </div>

                  <CustomScrollArea className="mb-4 max-h-[50vh] flex-grow">
                    <div className="space-y-4 p-2">
                      {missingFields.map((field) => {
                        const fieldId = `${field.nodeId}_${field.name}`;
                        // Consider fields required unless explicitly marked as optional
                        const isRequired = field.required !== false;

                        return (
                          <div key={fieldId} className="space-y-2">
                            <div className="flex items-center justify-between">
                              <Label
                                htmlFor={fieldId}
                                className="flex items-center gap-2 text-sm font-medium"
                              >
                                {field.nodeName || "Unknown Node"}:{" "}
                                {field.displayName || field.name || "Unnamed Field"}
                                <Badge variant="outline" className="text-xs">
                                  {field.inputType}
                                </Badge>
                                {isRequired ? (
                                  <Badge variant="destructive" className="text-xs">
                                    Required
                                  </Badge>
                                ) : (
                                  <Badge variant="outline" className="text-xs">
                                    Optional
                                  </Badge>
                                )}
                                {field.directly_connected_to_start && (
                                  <Badge
                                    variant="secondary"
                                    className="bg-blue-100 text-xs text-blue-800 dark:bg-blue-900 dark:text-blue-300"
                                  >
                                    Connected to Start
                                  </Badge>
                                )}
                              </Label>
                            </div>

                            {field.info && (
                              <p className="text-muted-foreground text-xs">{field.info}</p>
                            )}

                            {field.inputType === "boolean" ? (
                              <div className="flex items-center space-x-2">
                                <input
                                  id={fieldId}
                                  type="checkbox"
                                  checked={fieldValues[fieldId] === true}
                                  onChange={(e) => {
                                    // For boolean fields, ensure we're passing a proper boolean value
                                    const boolValue = e.target.checked;
                                    console.log(
                                      `Boolean field ${fieldId} changed to: ${boolValue}`,
                                    );
                                    handleFieldChange(fieldId, boolValue, field.inputType);
                                  }}
                                  className="text-primary focus:ring-primary h-4 w-4 rounded border-gray-300"
                                />
                                <Label htmlFor={fieldId} className="text-sm">
                                  {field.displayName}
                                </Label>
                              </div>
                            ) : field.inputType === "object" ||
                              field.inputType === "dict" ||
                              field.inputType === "json" ? (
                              // Simple JSON input for object fields
                              <div className="space-y-2">
                                <div className="mb-2 flex items-center gap-2">
                                  <Code className="h-4 w-4 text-blue-500" />
                                  <p className="text-muted-foreground text-xs">
                                    Enter JSON object (e.g., {`{"key": "value", "number": 123}`})
                                  </p>
                                </div>
                                <Textarea
                                  id={fieldId}
                                  value={(() => {
                                    const currentValue = fieldValues[fieldId];
                                    // Handle wrapped values
                                    let unwrappedValue = currentValue;
                                    if (
                                      typeof currentValue === "object" &&
                                      currentValue !== null &&
                                      "value" in currentValue
                                    ) {
                                      unwrappedValue = currentValue.value;
                                    }

                                    // Convert object to JSON string for display
                                    if (
                                      typeof unwrappedValue === "object" &&
                                      unwrappedValue !== null
                                    ) {
                                      try {
                                        return JSON.stringify(unwrappedValue, null, 2);
                                      } catch (e) {
                                        return "{}";
                                      }
                                    }
                                    // If it's already a string, use it as-is
                                    return unwrappedValue || "{}";
                                  })()}
                                  onChange={(e) => {
                                    const jsonString = e.target.value;

                                    // Try to parse the JSON to validate it
                                    try {
                                      if (jsonString.trim() === "") {
                                        // Empty input - pass empty object
                                        handleFieldChange(fieldId, {}, field.inputType);
                                      } else {
                                        // Parse and pass the native object
                                        const parsedObject = JSON.parse(jsonString);
                                        handleFieldChange(fieldId, parsedObject, field.inputType);
                                      }
                                    } catch (e) {
                                      // Invalid JSON - still update the field value to show the user's input
                                      // but the validation will catch this error
                                      handleFieldChange(fieldId, jsonString, field.inputType);
                                    }
                                  }}
                                  placeholder={`Enter JSON object...`}
                                  className={`font-mono text-sm ${errors[fieldId] ? "border-red-500" : ""}`}
                                  rows={4}
                                />
                              </div>
                            ) : field.inputType === "array" || field.inputType === "list" ? (
                              <Textarea
                                id={fieldId}
                                value={fieldValues[fieldId] || ""}
                                onChange={(e) =>
                                  handleFieldChange(fieldId, e.target.value, field.inputType)
                                }
                                placeholder={`Enter ${field.displayName || field.name || "value"}...`}
                                className={`font-mono text-sm ${errors[fieldId] ? "border-red-500" : ""}`}
                                rows={4}
                              />
                            ) : field.inputType === "number" ||
                              field.inputType === "int" ||
                              field.inputType === "float" ? (
                              <Input
                                id={fieldId}
                                type="number"
                                value={fieldValues[fieldId] || ""}
                                onChange={(e) =>
                                  handleFieldChange(fieldId, e.target.value, field.inputType)
                                }
                                placeholder={`Enter ${field.displayName || field.name || "value"}...`}
                                className={errors[fieldId] ? "border-red-500" : ""}
                              />
                            ) : (
                              <Input
                                id={fieldId}
                                value={fieldValues[fieldId] || ""}
                                onChange={(e) =>
                                  handleFieldChange(fieldId, e.target.value, field.inputType)
                                }
                                placeholder={`Enter ${field.displayName || field.name || "value"}...`}
                                className={errors[fieldId] ? "border-red-500" : ""}
                              />
                            )}

                            {errors[fieldId] && (
                              <p className="text-xs text-red-500">{errors[fieldId]}</p>
                            )}
                          </div>
                        );
                      })}
                    </div>
                  </CustomScrollArea>

                  <div className="mt-4 flex justify-end">
                    <button
                      type="button"
                      onClick={handleSubmit}
                      disabled={!isFormValid || isExecuting}
                      className={`bg-primary text-primary-foreground hover:bg-primary/90 inline-flex h-9 items-center justify-center gap-1 rounded-md px-4 py-2 text-sm font-medium shadow-xs disabled:pointer-events-none disabled:opacity-50 ${!isFormValid || isExecuting ? "cursor-not-allowed opacity-50" : ""}`}
                    >
                      <Play className="h-4 w-4" />
                      Run Workflow
                    </button>
                  </div>
                </>
              ) : (
                <div className="flex flex-grow items-center justify-center">
                  <div className="p-6 text-center">
                    <CheckCircle className="mx-auto mb-4 h-12 w-12 text-green-500" />
                    <h3 className="mb-2 text-lg font-medium">No Parameters Found</h3>
                    <p className="text-muted-foreground mb-4 text-sm">
                      No workflow parameters were found. This is unusual - most workflows require
                      parameters.
                    </p>
                    <div className="flex justify-center gap-3">
                      <button
                        type="button"
                        onClick={() => {
                          // Close dialog to let user check the workflow
                          onClose();
                        }}
                        className="bg-background hover:bg-accent hover:text-accent-foreground inline-flex h-9 items-center justify-center gap-1 rounded-md border px-4 py-2 text-sm font-medium shadow-xs"
                      >
                        <FileText className="mr-1 h-4 w-4" />
                        Check Workflow
                      </button>
                      <button
                        type="button"
                        onClick={() => {
                          setActiveTab("logs");
                          handleSubmit();
                        }}
                        className="bg-primary text-primary-foreground hover:bg-primary/90 inline-flex h-9 items-center justify-center gap-1 rounded-md px-4 py-2 text-sm font-medium shadow-xs"
                      >
                        <Play className="mr-1 h-4 w-4" />
                        Execute Anyway
                      </button>
                    </div>
                  </div>
                </div>
              )}
            </TabsContent>

            {/* Logs Tab */}
            <TabsContent value="logs" className="mt-4 flex flex-grow flex-col overflow-hidden">
              <div className="mb-2 flex items-center justify-between">
                <h3 className="text-sm font-medium">Execution Logs</h3>
                {logs.length > 0 && (
                  <button
                    type="button"
                    onClick={handleDownloadLogs}
                    className="bg-background hover:bg-accent hover:text-accent-foreground inline-flex h-7 items-center justify-center gap-1 rounded-md border px-2 text-sm text-xs font-medium shadow-xs"
                  >
                    <Download className="mr-1 h-3 w-3" />
                    Download Logs
                  </button>
                )}
              </div>

              <Card className="flex-grow overflow-hidden border">
                <CardContent className="p-0">
                  {/* Approval UI */}
                  {approvalNeeded && approvalDetails && (
                    <div className="p-2">
                      <ApprovalRequest
                        correlationId={approvalDetails.correlationId}
                        nodeId={approvalDetails.nodeId}
                        nodeName={approvalDetails.nodeName}
                        onApprovalSent={handleApprovalSent}
                      />
                    </div>
                  )}

                  <LogDisplay logs={logs} showStreamingStatus={true} />

                  {/* Debug Panel for Approval State (only in development mode) */}
                  {/* {process.env.NODE_ENV === "development" && (
                    <details className="mt-2 border-t pt-2 text-xs" open>
                      <summary className="cursor-pointer font-medium">Debug Approval State</summary>
                      <pre className="mt-2 overflow-auto rounded bg-gray-100 p-2">
                        {JSON.stringify(
                          {
                            approvalNeeded,
                            approvalDetails,
                            correlationId,
                            logsWithApproval: logs.filter(
                              (log) =>
                                log.toLowerCase().includes("waiting_for_approval") &&
                                log.toLowerCase().includes("approval_required") &&
                                log.toLowerCase().includes("status") &&
                                log.toLowerCase().includes("paused"),
                            ).length,
                            validApprovalLogs: logs.filter((log) => {
                              try {
                                const data = JSON.parse(log);
                                return (
                                  data.workflow_status === "waiting_for_approval" &&
                                  data.approval_required === true &&
                                  data.status === "paused"
                                );
                              } catch (e) {
                                return false;
                              }
                            }).length,
                            pendingApproval: window._pendingApproval,
                            lastApprovalTimestamp: window._lastApprovalTimestamp,
                            approvalEventHistory: window._approvalEventHistory,
                            currentTimestamp: Date.now(),
                          },
                          null,
                          2,
                        )}
                      </pre>
                      <div className="mt-2 flex gap-2">
                        <button
                          onClick={() => {
                            if (window._pendingApproval) {
                              setApprovalNeeded(true);
                              setApprovalDetails({
                                correlationId: window._pendingApproval.correlationId,
                                nodeId: window._pendingApproval.nodeId,
                                nodeName: window._pendingApproval.nodeName,
                                timestamp: window._pendingApproval.timestamp,
                              });
                              console.log("Manually triggered approval UI from debug panel");

                              // Add to history
                              if (window._approvalEventHistory) {
                                window._approvalEventHistory.push({
                                  correlationId: window._pendingApproval.correlationId,
                                  nodeId: window._pendingApproval.nodeId,
                                  nodeName: window._pendingApproval.nodeName,
                                  timestamp: Date.now(),
                                  status: "manually_triggered",
                                });
                              }
                            } else {
                              console.log("No pending approval to trigger");

                              // Check logs for a valid approval request
                              const validApprovalLogs = logs.filter((log) => {
                                try {
                                  const data = JSON.parse(log);
                                  return (
                                    data.workflow_status === "waiting_for_approval" &&
                                    data.approval_required === true &&
                                    data.status === "paused" &&
                                    data.node_id
                                  );
                                } catch (e) {
                                  return false;
                                }
                              });

                              if (validApprovalLogs.length > 0) {
                                try {
                                  const logData = JSON.parse(validApprovalLogs[0]);
                                  setApprovalNeeded(true);
                                  setApprovalDetails({
                                    correlationId: correlationId || "",
                                    nodeId: logData.node_id,
                                    nodeName: logData.node_name || logData.node_id,
                                    timestamp: Date.now(),
                                  });
                                  console.log("Manually triggered approval UI from logs");
                                } catch (e) {
                                  console.error("Error parsing log data:", e);
                                }
                              }
                            }
                          }}
                          className="rounded bg-blue-100 px-2 py-1 text-xs text-blue-800"
                        >
                          Force Show Approval
                        </button>
                        <button
                          onClick={() => {
                            clearAllApprovalEvents();
                            setApprovalNeeded(false);
                            setApprovalDetails(null);
                            console.log("Reset approval state from debug panel");
                          }}
                          className="rounded bg-red-100 px-2 py-1 text-xs text-red-800"
                        >
                          Reset Approval State
                        </button>
                      </div>
                    </details>
                  )} */}

                  {/* Dialog Content Warnings */}
                  {logs.some((log) => log.includes("Missing 'Description'")) && (
                    <div className="border-t p-2">
                      <div className="mb-2 flex items-start gap-2 rounded-md border border-yellow-200 bg-yellow-50 p-2">
                        <AlertCircle className="mt-0.5 h-4 w-4 flex-shrink-0 text-yellow-500" />
                        <div className="flex-1">
                          <p className="text-xs font-medium text-yellow-800">
                            Dialog Content Warnings
                          </p>
                          <p className="text-xs text-yellow-700">
                            Missing descriptions for dialog content. This won't prevent execution
                            but should be fixed for better user experience.
                          </p>
                        </div>
                        <button
                          type="button"
                          onClick={addDefaultDescriptions}
                          className="rounded bg-yellow-100 px-2 py-1 text-xs text-yellow-800 hover:bg-yellow-200"
                        >
                          Add Default Descriptions
                        </button>
                      </div>
                    </div>
                  )}

                  {correlationId && (
                    <div className="text-muted-foreground border-t p-2 text-xs">
                      <span className="font-medium">Correlation ID:</span> {correlationId}
                    </div>
                  )}
                </CardContent>
              </Card>

              <div className="mt-4 flex justify-between">
                {missingFields.length > 0 && (
                  <button
                    type="button"
                    onClick={() => setActiveTab("parameters")}
                    className="bg-background hover:bg-accent hover:text-accent-foreground inline-flex h-9 items-center justify-center gap-1 rounded-md border px-4 py-2 text-sm font-medium shadow-xs"
                  >
                    <FileText className="mr-1 h-4 w-4" />
                    Back to Parameters
                  </button>
                )}

                {/* Always show Run Again button regardless of missing fields */}
                <button
                  type="button"
                  onClick={handleSubmit}
                  disabled={!isFormValid || isStreaming}
                  className={`bg-primary text-primary-foreground hover:bg-primary/90 inline-flex h-9 items-center justify-center gap-1 rounded-md px-4 py-2 text-sm font-medium shadow-xs disabled:pointer-events-none disabled:opacity-50 ${!isFormValid || isStreaming ? "cursor-not-allowed opacity-50" : ""} ${missingFields.length === 0 ? "ml-auto" : ""}`}
                >
                  <Play className="h-4 w-4" />
                  {isStreaming ? "Executing..." : "Run Again"}
                </button>
              </div>
            </TabsContent>
          </Tabs>
        </div>

        <DialogFooter className="mt-4 flex items-center justify-between">
          {/* Left side - Stop Execution button (shown when streaming) or Approval buttons */}
          <div className="flex items-center gap-2">
            {/* Show stop execution button when streaming and not waiting for approval */}
            {isStreaming && onStopExecution && workflowStatus !== "waiting_for_approval" && (
              <button
                type="button"
                onClick={async () => {
                  // Add a log entry before stopping
                  const timestamp = new Date().toISOString().substring(11, 19);
                  addLog(`[${timestamp}] 🛑 Sending request to stop workflow execution...`);

                  // Call the onStopExecution prop
                  if (onStopExecution) {
                    onStopExecution();
                  }
                }}
                className="inline-flex h-9 items-center justify-center gap-1.5 rounded-md bg-red-600 px-4 py-2 text-sm font-medium text-white shadow-xs hover:bg-red-700"
                title="Stop the current workflow execution"
              >
                <Square className="h-4 w-4" />
                Stop Workflow Execution
              </button>
            )}

            {/* Show approve and reject buttons only when status is waiting_for_approval */}
            {workflowStatus === "waiting_for_approval" && approvalNeeded && approvalDetails && (
              <>
                <button
                  type="button"
                  onClick={() => {
                    // Import the API function
                    import("@/lib/api").then(({ sendApprovalDecision }) => {
                      // Send approval decision with "approve" status
                      sendApprovalDecision(approvalDetails.correlationId, "approve").then(
                        (result) => {
                          if (result.success) {
                            // Handle approval sent
                            handleApprovalSent();
                            // Add log entry
                            addLog(`✅ Approval sent for node: ${approvalDetails.nodeName}`);
                          } else {
                            // Add error log entry
                            addLog(`❌ Error sending approval: ${result.error}`);
                          }
                        },
                      );
                    });
                  }}
                  className="inline-flex h-9 items-center justify-center gap-1.5 rounded-md bg-green-600 px-4 py-2 text-sm font-medium text-white shadow-xs hover:bg-green-700"
                  title="Approve this workflow step"
                >
                  <CheckCircle className="h-4 w-4" />
                  Approve
                </button>
                <button
                  type="button"
                  onClick={() => {
                    // Import the API function
                    import("@/lib/api").then(({ sendApprovalDecision }) => {
                      // Send approval decision with "reject" status
                      sendApprovalDecision(approvalDetails.correlationId, "reject").then(
                        (result) => {
                          if (result.success) {
                            // Handle approval sent
                            handleApprovalSent();
                            // Add log entry
                            addLog(`❌ Rejection sent for node: ${approvalDetails.nodeName}`);
                          } else {
                            // Add error log entry
                            addLog(`❌ Error sending rejection: ${result.error}`);
                          }
                        },
                      );
                    });
                  }}
                  className="inline-flex h-9 items-center justify-center gap-1.5 rounded-md bg-red-600 px-4 py-2 text-sm font-medium text-white shadow-xs hover:bg-red-700"
                  title="Reject this workflow step"
                >
                  <AlertCircle className="h-4 w-4" />
                  Reject
                </button>
              </>
            )}
          </div>

          {/* Right side - Close button */}
          <button
            type="button"
            onClick={onClose}
            className="bg-background hover:bg-accent hover:text-accent-foreground inline-flex h-9 items-center justify-center rounded-md border px-4 py-2 text-sm font-medium shadow-xs"
            title="Close this dialog without stopping execution"
          >
            Close
          </button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
