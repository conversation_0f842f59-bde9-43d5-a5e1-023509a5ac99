import React, { useRef, useEffect } from "react";
import { CustomScrollArea } from "@/components/ui/custom-scroll-area";
import { AlertCircle, CheckCircle, Info, XCircle, Loader2, ShieldCheck } from "lucide-react";
import { useExecutionStore } from "@/store/executionStore";
import { dispatchApprovalNeededEvent } from "@/lib/approvalUtils";

interface LogDisplayProps {
  logs: string[];
  showStreamingStatus?: boolean;
}

export function LogDisplay({ logs = [], showStreamingStatus = true }: LogDisplayProps) {
  const scrollRef = useRef<HTMLDivElement>(null);
  const { isStreaming } = useExecutionStore();

  // Ensure logs is always an array of strings, converting any non-string logs
  const stringLogs = (Array.isArray(logs) ? logs : []).map((log) => {
    if (typeof log === "string") {
      return log;
    }
    if (log === null || log === undefined) {
      return ""; // Return an empty string for null/undefined logs
    }
    // Pretty-print objects for better readability in the log display
    if (typeof log === "object") {
      return JSON.stringify(log, null, 2);
    }
    // Coerce any other types to string as a fallback
    return String(log);
  });

  // Determine workflow status from logs
  const getWorkflowStatus = (): {
    status: "in_progress" | "completed" | "failed" | "cancelled" | "waiting_for_approval";
    message: string;
    nodeId?: string;
    nodeName?: string;
  } => {
    // Default status
    let result = {
      status: "in_progress" as const,
      message: "Execution in progress...",
    };

    // Check logs for workflow status, from newest to oldest
    for (let i = stringLogs.length - 1; i >= 0; i--) {
      const log = stringLogs[i];
      let logData;

      // Try to parse the log as JSON
      try {
        logData = JSON.parse(log);
      } catch (e) {
        // Not a JSON string, might be one of our formatted messages
        // Fallback to string matching for non-JSON logs
        const lowerCaseLog = log.toLowerCase();
        if (lowerCaseLog.includes("workflow failed")) {
          return { status: "failed", message: "Execution failed" };
        } else if (lowerCaseLog.includes("workflow completed")) {
          return { status: "completed", message: "Execution completed successfully" };
        } else if (lowerCaseLog.includes("workflow cancelled")) {
          return { status: "cancelled", message: "Execution was cancelled" };
        }
        continue; // Move to the next log if it's not JSON
      }

      // If it's a JSON log, check the workflow_status
      if (logData && logData.workflow_status) {
        const status = logData.workflow_status.toLowerCase();
        switch (status) {
          case "failed":
            return { status: "failed", message: "Execution failed" };
          case "completed":
            return { status: "completed", message: "Execution completed successfully" };
          case "cancelled":
            return { status: "cancelled", message: "Execution was cancelled" };
          case "waiting_for_approval":
            // For approval, we need more specific checks
            if (logData.approval_required === true && logData.status === "paused") {
              return {
                status: "waiting_for_approval",
                message: "Waiting for approval",
                nodeId: logData.node_id || undefined,
                nodeName: logData.node_name || logData.node_id || "Unknown Node",
              };
            }
            break;
          default:
            // For other statuses like 'running', we continue checking previous logs
            break;
        }
      }
    }

    return result;
  };

  // Get current workflow status
  const workflowStatus = getWorkflowStatus();
  const { correlationId } = useExecutionStore();

  // Auto-scroll to bottom when logs update
  useEffect(() => {
    if (scrollRef.current) {
      scrollRef.current.scrollIntoView({ behavior: "smooth" });
    }
  }, [stringLogs]);

  // Dispatch approval needed event when status changes to waiting_for_approval
  useEffect(() => {
    if (workflowStatus.status === "waiting_for_approval" && correlationId && workflowStatus.nodeId) {
      console.log("LogDisplay: Detected valid waiting_for_approval status, dispatching event");

      // Only dispatch if we have a valid node ID (not undefined or "unknown")
      if (workflowStatus.nodeId && workflowStatus.nodeId !== "unknown") {
        // Use the centralized function to dispatch the event
        dispatchApprovalNeededEvent(
          correlationId,
          workflowStatus.nodeId,
          workflowStatus.nodeName || workflowStatus.nodeId
        );

        // Also set the window flag directly for immediate access
        window._pendingApproval = {
          correlationId,
          nodeId: workflowStatus.nodeId,
          nodeName: workflowStatus.nodeName || workflowStatus.nodeId,
          timestamp: Date.now()
        };

        // Force a UI update
        setTimeout(() => {
          window.dispatchEvent(new CustomEvent('approval-ui-update'));
        }, 200);

        // Log the approval request for debugging
        console.log("Dispatched approval event for:", {
          correlationId,
          nodeId: workflowStatus.nodeId,
          nodeName: workflowStatus.nodeName || workflowStatus.nodeId
        });
      } else {
        console.log("Skipping approval event dispatch due to missing node ID");
      }
    }
  }, [workflowStatus.status, correlationId, workflowStatus.nodeId, workflowStatus.nodeName]);

  // Parse log line to determine type and format
  const parseLogLine = (line: string) => {
    // Default values
    let type = "info";
    let icon = <Info className="h-4 w-4 flex-shrink-0 text-blue-500" />;
    let className = "text-blue-800 bg-blue-50 border-blue-100";

    // Check for error messages
    if (
      line.toLowerCase().includes("error") ||
      line.toLowerCase().includes("exception") ||
      line.toLowerCase().includes("failed")
    ) {
      type = "error";
      icon = <XCircle className="h-4 w-4 flex-shrink-0 text-red-500" />;
      className = "text-red-800 bg-red-50 border-red-100";
    }
    // Check for success messages
    else if (
      line.toLowerCase().includes("success") ||
      line.toLowerCase().includes("completed") ||
      line.toLowerCase().includes("finished")
    ) {
      type = "success";
      icon = <CheckCircle className="h-4 w-4 flex-shrink-0 text-green-500" />;
      className = "text-green-800 bg-green-50 border-green-100";
    }
    // Check for warning messages
    else if (line.toLowerCase().includes("warning") || line.toLowerCase().includes("warn")) {
      type = "warning";
      icon = <AlertCircle className="h-4 w-4 flex-shrink-0 text-yellow-500" />;
      className = "text-yellow-800 bg-yellow-50 border-yellow-100";
    }

    return { type, icon, className };
  };

  // Filter out duplicate "Workflow is waiting for approval" messages
  const filteredLogs = stringLogs.reduce((acc: string[], log: string, index: number) => {
    // Skip duplicate "Workflow is waiting for approval" messages
    if (
      log.startsWith("⏸️ Workflow is waiting for approval") &&
      index > 0 &&
      stringLogs[index - 1].startsWith("⏸️ Workflow is waiting for approval")
    ) {
      return acc;
    }

    // Add the log to the filtered list
    acc.push(log);
    return acc;
  }, []);

  return (
    <CustomScrollArea className="h-[300px] p-0">
      <div className="space-y-1 p-4 font-mono text-xs">
        {filteredLogs.length === 0 ? (
          <div className="text-muted-foreground py-8 text-center">
            <Info className="mx-auto mb-2 h-8 w-8 opacity-50" />
            <p>No logs available. Run the workflow to see execution logs.</p>
          </div>
        ) : (
          <>
            {filteredLogs.map((log, index) => {
              const { icon, className } = parseLogLine(log);
              return (
                <div
                  key={index}
                  className={`flex items-start gap-2 rounded border p-2 ${className}`}
                >
                  {icon}
                  <span className="break-all whitespace-pre-wrap">{log}</span>
                </div>
              );
            })}

            {/* Streaming status indicator */}
            {showStreamingStatus && isStreaming && (
              <div className="flex items-center gap-2 rounded border border-blue-100 bg-blue-50 p-2 text-blue-800">
                <Loader2 className="h-4 w-4 animate-spin text-blue-500" />
                <span>Streaming logs in real-time...</span>
              </div>
            )}

            {/* Execution status indicator */}
            {showStreamingStatus &&
              !isStreaming &&
              stringLogs.length > 0 &&
              workflowStatus.status !== "in_progress" && (
                <div
                  className={`flex items-center gap-2 rounded border p-2 ${
                    workflowStatus.status === "failed"
                      ? "border-red-100 bg-red-50 text-red-800 dark:border-red-800 dark:bg-red-900/20 dark:text-red-300"
                      : workflowStatus.status === "completed"
                        ? "border-green-100 bg-green-50 text-green-800 dark:border-green-800 dark:bg-green-900/20 dark:text-green-300"
                        : workflowStatus.status === "cancelled"
                          ? "border-yellow-100 bg-yellow-50 text-yellow-800 dark:border-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-300"
                          : workflowStatus.status === "waiting_for_approval"
                            ? "border-blue-100 bg-blue-50 text-blue-800 dark:border-blue-800 dark:bg-blue-900/20 dark:text-blue-300"
                            : "border-green-100 bg-green-50 text-green-800 dark:border-green-800 dark:bg-green-900/20 dark:text-green-300"
                  }`}
                >
                  {workflowStatus.status === "failed" ? (
                    <XCircle className="h-4 w-4 text-red-500" />
                  ) : workflowStatus.status === "completed" ? (
                    <CheckCircle className="h-4 w-4 text-green-500" />
                  ) : workflowStatus.status === "cancelled" ? (
                    <AlertCircle className="h-4 w-4 text-yellow-500" />
                  ) : workflowStatus.status === "waiting_for_approval" ? (
                    <ShieldCheck className="h-4 w-4 text-blue-500" />
                  ) : (
                    <CheckCircle className="h-4 w-4 text-green-500" />
                  )}
                  <span>{workflowStatus.message}</span>
                </div>
              )}
          </>
        )}
        <div ref={scrollRef} />
      </div>
    </CustomScrollArea>
  );
}
